<template>
  <div class="treatment-query">
    <div class="page-header">
      <h2>诊疗信息查询</h2>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="诊疗项目名称">
          <el-input v-model="searchForm.name" placeholder="请输入诊疗项目名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="项目类别">
          <el-select v-model="searchForm.category" placeholder="请选择类别" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="检验" value="检验"></el-option>
            <el-option label="检查" value="检查"></el-option>
            <el-option label="治疗" value="治疗"></el-option>
            <el-option label="手术" value="手术"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="价格范围">
          <el-input v-model="searchForm.minPrice" placeholder="最低价格" style="width: 100px;"></el-input>
          <span style="margin: 0 10px;">-</span>
          <el-input v-model="searchForm.maxPrice" placeholder="最高价格" style="width: 100px;"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchTreatments">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-table 
      :data="tableData" 
      border 
      style="width: 100%"
      v-loading="loading">
      <el-table-column prop="id" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="category" label="类别" width="100" align="center">
        <template #default="{ row }">
          <el-tag 
            :type="getCategoryType(row.category)">
            {{ row.category }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="code" label="项目编码" width="120"></el-table-column>
      <el-table-column prop="nationalCode" label="国家编码" width="150" show-overflow-tooltip></el-table-column>
      <el-table-column prop="name" label="项目名称" min-width="200"></el-table-column>
      <el-table-column prop="description" label="项目描述" min-width="200" show-overflow-tooltip></el-table-column>
      <el-table-column prop="unit" label="单位" width="80" align="center"></el-table-column>
      <el-table-column prop="price" label="价格" width="100" align="center">
        <template #default="{ row }">
          ¥{{ row.price }}
        </template>
      </el-table-column>
      <el-table-column prop="department" label="执行科室" width="120"></el-table-column>
      <el-table-column prop="insuranceRatio" label="报销比例" width="100" align="center">
        <template #default="{ row }">
          {{ row.insuranceRatio }}%
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" width="120" align="center" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="viewDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog title="诊疗项目详情" v-model="detailDialogVisible" width="50%">
      <el-descriptions :column="2" border v-if="selectedTreatment">
        <el-descriptions-item label="项目类别">{{ selectedTreatment.category }}</el-descriptions-item>
        <el-descriptions-item label="项目编码">{{ selectedTreatment.code }}</el-descriptions-item>
        <el-descriptions-item label="国家编码" :span="2">{{ selectedTreatment.nationalCode }}</el-descriptions-item>
        <el-descriptions-item label="项目名称" :span="2">{{ selectedTreatment.name }}</el-descriptions-item>
        <el-descriptions-item label="项目描述" :span="2">{{ selectedTreatment.description }}</el-descriptions-item>
        <el-descriptions-item label="单位">{{ selectedTreatment.unit }}</el-descriptions-item>
        <el-descriptions-item label="价格">¥{{ selectedTreatment.price }}</el-descriptions-item>
        <el-descriptions-item label="执行科室">{{ selectedTreatment.department }}</el-descriptions-item>
        <el-descriptions-item label="报销比例">{{ selectedTreatment.insuranceRatio }}%</el-descriptions-item>
        <el-descriptions-item label="异常内容" :span="2">{{ selectedTreatment.exceptionContent || '无' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ selectedTreatment.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TreatmentQuery',
  data() {
    return {
      searchForm: {
        name: '',
        category: '',
        minPrice: '',
        maxPrice: ''
      },
      tableData: [
        {
          id: '1',
          category: '检验',
          code: '250100001',
          nationalCode: '4533000000100\n00-250100001',
          name: '血常规',
          description: '血液常规检查',
          unit: '次',
          price: '25.00',
          department: '检验科',
          insuranceRatio: 90,
          exceptionContent: '',
          remark: '血液三分类检查'
        },
        {
          id: '2',
          category: '检查',
          code: '330300003',
          nationalCode: '4533000000200\n00-330300003',
          name: '心电图',
          description: '心电图检查',
          unit: '次',
          price: '30.00',
          department: '心电图室',
          insuranceRatio: 85,
          exceptionContent: '',
          remark: '常规心电图'
        },
        {
          id: '3',
          category: '检查',
          code: '440400004',
          nationalCode: '4533000000300\n00-440400004',
          name: '胸部CT',
          description: 'CT胸部平扫',
          unit: '次',
          price: '280.00',
          department: '放射科',
          insuranceRatio: 80,
          exceptionContent: '',
          remark: 'CT平扫检查'
        },
        {
          id: '4',
          category: '检查',
          code: '550500005',
          nationalCode: '4533000000400\n00-550500005',
          name: '腹部B超',
          description: '腹部超声检查',
          unit: '次',
          price: '120.00',
          department: '超声科',
          insuranceRatio: 85,
          exceptionContent: '',
          remark: '腹部彩超检查'
        },
        {
          id: '5',
          category: '治疗',
          code: '660600006',
          nationalCode: '4533000000500\n00-660600006',
          name: '静脉输液',
          description: '静脉输液治疗',
          unit: '次',
          price: '15.00',
          department: '护理部',
          insuranceRatio: 95,
          exceptionContent: '',
          remark: '静脉输液护理'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 5,
      loading: false,
      detailDialogVisible: false,
      selectedTreatment: null
    }
  },
  methods: {
    searchTreatments() {
      this.loading = true;
      setTimeout(() => {
        console.log('搜索条件:', this.searchForm);
        // 这里应该调用API进行搜索
        this.loading = false;
        this.$message.success('查询完成');
      }, 1000);
    },
    
    resetSearch() {
      this.searchForm = {
        name: '',
        category: '',
        minPrice: '',
        maxPrice: ''
      };
      this.searchTreatments();
    },
    
    viewDetail(row) {
      this.selectedTreatment = row;
      this.detailDialogVisible = true;
    },
    
    getCategoryType(category) {
      const categoryMap = {
        '检验': 'primary',
        '检查': 'success',
        '治疗': 'warning',
        '手术': 'danger'
      };
      return categoryMap[category] || 'info';
    },
    
    handleSizeChange(val) {
      this.pageSize = val;
      this.searchTreatments();
    },
    
    handleCurrentChange(val) {
      this.currentPage = val;
      this.searchTreatments();
    }
  },
  
  mounted() {
    this.searchTreatments();
  }
}
</script>

<style scoped>
.treatment-query {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  font-size: 24px;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
