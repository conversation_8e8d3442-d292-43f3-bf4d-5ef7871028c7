<template>
  <div>
    <el-form :inline="true" :model="searchForm" class="demo-form-inline">
      <el-form-item label="药品名称">
        <el-input v-model="searchForm.name" placeholder="药品名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-button type="primary" @click="addDrug">+ 增加药品</el-button>
    <el-button type="danger" @click="deleteSelected">删除选中</el-button>

    <el-table 
      ref="multipleTable"
      :data="tableData" 
      border 
      style="width: 100%"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="id" label="序号" width="50"></el-table-column>
      <el-table-column prop="type" label="药品类型" width="80"></el-table-column>
      <el-table-column prop="name" label="药品名称"></el-table-column>
      <el-table-column prop="brand" label="品名"></el-table-column>
      <el-table-column prop="spec" label="规格"></el-table-column>
      <el-table-column prop="unit" label="单位" width="80"></el-table-column>
      <el-table-column prop="price" label="价格" width="100"></el-table-column>
      <el-table-column prop="manufacturer" label="生产厂家"></el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">修改</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>

    <!-- 修改药品弹窗 -->
    <el-dialog title="修改药品" v-model="editDialogVisible" width="50%">
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="药品分类">
          <el-select v-model="editForm.type" placeholder="请选择">
            <el-option label="甲类" value="甲类"></el-option>
            <el-option label="乙类" value="乙类"></el-option>
            <el-option label="丙类" value="丙类"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="*药品中文名称">
          <el-input v-model="editForm.name"></el-input>
        </el-form-item>
        <el-form-item label="*品名">
          <el-input v-model="editForm.brand"></el-input>
        </el-form-item>
        <el-form-item label="*规格">
          <el-input v-model="editForm.spec"></el-input>
        </el-form-item>
        <el-form-item label="*单位">
          <el-input v-model="editForm.unit"></el-input>
        </el-form-item>
        <el-form-item label="*生产企业">
          <el-input v-model="editForm.manufacturer"></el-input>
        </el-form-item>
        <el-form-item label="*价格">
          <el-input-number v-model="editForm.price" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="editForm.remark"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitEdit">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 增加药品弹窗 -->
    <el-dialog title="增加药品" v-model="addDialogVisible" width="50%">
      <el-form :model="addForm" label-width="100px">
        <el-form-item label="药品分类">
          <el-select v-model="addForm.type" placeholder="请选择">
            <el-option label="甲类" value="甲类"></el-option>
            <el-option label="乙类" value="乙类"></el-option>
            <el-option label="丙类" value="丙类"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="*药品中文名称">
          <el-input v-model="addForm.name"></el-input>
        </el-form-item>
        <el-form-item label="*品名">
          <el-input v-model="addForm.brand"></el-input>
        </el-form-item>
        <el-form-item label="*规格">
          <el-input v-model="addForm.spec"></el-input>
        </el-form-item>
        <el-form-item label="*单位">
          <el-input v-model="addForm.unit"></el-input>
        </el-form-item>
        <el-form-item label="*生产企业">
          <el-input v-model="addForm.manufacturer"></el-input>
        </el-form-item>
        <el-form-item label="*价格">
          <el-input-number v-model="addForm.price" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="addForm.remark"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitAdd">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        name: ''
      },
      tableData: [
        {
          id: '1',
          type: '甲类',
          name: '阿司匹林',
          brand: '拜阿司匹灵',
          spec: '100mg*30片',
          unit: '盒',
          price: '15.80',
          manufacturer: '拜耳医药保健有限公司'
        },
        {
          id: '2',
          type: '乙类',
          name: '奥美拉唑',
          brand: '洛赛克',
          spec: '20mg*14粒',
          unit: '盒',
          price: '45.60',
          manufacturer: '阿斯利康制药有限公司'
        },
        {
          id: '3',
          type: '丙类',
          name: '奥比帕利',
          brand: '维建乐',
          spec: '奥比他韦12.5mg,帕立瑞韦75mg,利托那韦50mg',
          unit: '盒',
          price: '2712.60',
          manufacturer: '爱尔兰Fournier Laboratories Ireland Limited'
        },
        {
          id: '4',
          type: '甲类',
          name: '青霉素',
          brand: '青霉素钠',
          spec: '80万单位',
          unit: '支',
          price: '2.50',
          manufacturer: '华北制药股份有限公司'
        },
        {
          id: '5',
          type: '乙类',
          name: '头孢克肟',
          brand: '世福素',
          spec: '100mg*6粒',
          unit: '盒',
          price: '28.90',
          manufacturer: '鲁南贝特制药有限公司'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 5,
      
      // 修改药品弹窗相关
      editDialogVisible: false,
      editForm: {},
      
      // 增加药品弹窗相关
      addDialogVisible: false,
      addForm: {
        id: '',
        type: '',
        name: '',
        brand: '',
        spec: '',
        unit: '',
        price: 0,
        manufacturer: '',
        remark: ''
      },
      
      // 新增：存储选中的行
      multipleSelection: []
    }
  },
  methods: {
    onSubmit() {
      console.log('搜索:', this.searchForm.name);
    },
    addDrug() {
      this.addDialogVisible = true;
      // 初始化新增表单数据
      this.addForm = {
        id: (this.tableData.length + 1).toString(),
        type: '',
        name: '',
        brand: '',
        spec: '',
        unit: '',
        price: 0,
        manufacturer: '',
        remark: ''
      };
    },
    
    // 处理选中行变化
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },
    
    // 删除选中行
    deleteSelected() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一项');
        return;
      }
      
      this.$confirm('确定要删除选中的药品吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 获取选中行的ID集合
        const selectedIds = this.multipleSelection.map(row => row.id);
        
        // 过滤掉选中的行
        this.tableData = this.tableData.filter(item => 
          !selectedIds.includes(item.id)
        );
        
        // 更新总数
        this.total = this.tableData.length;
        
        // 清空选中状态
        this.$refs.multipleTable.clearSelection();
        
        this.$message.success('删除成功');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    
    handleEdit(index, row) {
      this.editDialogVisible = true;
      this.editForm = { ...row };
    },
    submitEdit() {
      const index = this.tableData.findIndex(item => item.id === this.editForm.id);
      if (index !== -1) {
        this.tableData.splice(index, 1, this.editForm);
      }
      this.editDialogVisible = false;
    },
    submitAdd() {
      this.tableData.push({ ...this.addForm });
      this.total = this.tableData.length;
      this.addDialogVisible = false;
    },
    handleDelete(index, row) {
      this.$confirm('确定要删除该药品吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableData.splice(index, 1);
        this.total = this.tableData.length;
        this.$message.success('删除成功');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    }
  }
}
</script>

<style>
.el-table .cell {
  text-align: center;
}
</style>