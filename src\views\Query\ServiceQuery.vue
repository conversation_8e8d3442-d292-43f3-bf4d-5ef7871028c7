<template>
  <div class="service-query">
    <div class="page-header">
      <h2>医疗服务信息查询</h2>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="服务项目名称">
          <el-input v-model="searchForm.name" placeholder="请输入服务项目名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="服务类型">
          <el-select v-model="searchForm.serviceType" placeholder="请选择服务类型" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="护理服务" value="护理服务"></el-option>
            <el-option label="住院服务" value="住院服务"></el-option>
            <el-option label="手术服务" value="手术服务"></el-option>
            <el-option label="麻醉服务" value="麻醉服务"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="价格范围">
          <el-input v-model="searchForm.minPrice" placeholder="最低价格" style="width: 100px;"></el-input>
          <span style="margin: 0 10px;">-</span>
          <el-input v-model="searchForm.maxPrice" placeholder="最高价格" style="width: 100px;"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchServices">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-table 
      :data="tableData" 
      border 
      style="width: 100%"
      v-loading="loading">
      <el-table-column prop="id" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="serviceType" label="服务类型" width="120" align="center">
        <template #default="{ row }">
          <el-tag 
            :type="getServiceType(row.serviceType)">
            {{ row.serviceType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="code" label="服务编码" width="120"></el-table-column>
      <el-table-column prop="nationalCode" label="国家编码" width="150" show-overflow-tooltip></el-table-column>
      <el-table-column prop="name" label="服务项目名称" min-width="200"></el-table-column>
      <el-table-column prop="description" label="服务描述" min-width="200" show-overflow-tooltip></el-table-column>
      <el-table-column prop="unit" label="单位" width="80" align="center"></el-table-column>
      <el-table-column prop="price" label="价格" width="100" align="center">
        <template #default="{ row }">
          ¥{{ row.price }}
        </template>
      </el-table-column>
      <el-table-column prop="department" label="执行科室" width="120"></el-table-column>
      <el-table-column prop="insuranceRatio" label="报销比例" width="100" align="center">
        <template #default="{ row }">
          {{ row.insuranceRatio }}%
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" width="120" align="center" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="viewDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog title="医疗服务详情" v-model="detailDialogVisible" width="50%">
      <el-descriptions :column="2" border v-if="selectedService">
        <el-descriptions-item label="服务类型">{{ selectedService.serviceType }}</el-descriptions-item>
        <el-descriptions-item label="服务编码">{{ selectedService.code }}</el-descriptions-item>
        <el-descriptions-item label="国家编码" :span="2">{{ selectedService.nationalCode }}</el-descriptions-item>
        <el-descriptions-item label="服务项目名称" :span="2">{{ selectedService.name }}</el-descriptions-item>
        <el-descriptions-item label="服务描述" :span="2">{{ selectedService.description }}</el-descriptions-item>
        <el-descriptions-item label="单位">{{ selectedService.unit }}</el-descriptions-item>
        <el-descriptions-item label="价格">¥{{ selectedService.price }}</el-descriptions-item>
        <el-descriptions-item label="执行科室">{{ selectedService.department }}</el-descriptions-item>
        <el-descriptions-item label="报销比例">{{ selectedService.insuranceRatio }}%</el-descriptions-item>
        <el-descriptions-item label="异常内容" :span="2">{{ selectedService.exceptionContent || '无' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ selectedService.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ServiceQuery',
  data() {
    return {
      searchForm: {
        name: '',
        serviceType: '',
        minPrice: '',
        maxPrice: ''
      },
      tableData: [
        {
          id: '1',
          serviceType: '护理服务',
          code: 'NURSE001',
          nationalCode: '4533000000100\n00-NURSE001',
          name: '护理费',
          description: '住院护理服务费用',
          unit: '天',
          price: '50.00',
          department: '护理部',
          insuranceRatio: 95,
          exceptionContent: '',
          remark: '一级护理'
        },
        {
          id: '2',
          serviceType: '住院服务',
          code: 'BED001',
          nationalCode: '4533000000200\n00-BED001',
          name: '床位费',
          description: '住院床位使用费',
          unit: '天',
          price: '80.00',
          department: '住院部',
          insuranceRatio: 90,
          exceptionContent: '',
          remark: '普通病房'
        },
        {
          id: '3',
          serviceType: '手术服务',
          code: 'SURGERY001',
          nationalCode: '4533000000300\n00-SURGERY001',
          name: '手术费',
          description: '外科手术服务费',
          unit: '次',
          price: '1500.00',
          department: '手术室',
          insuranceRatio: 85,
          exceptionContent: '',
          remark: '三级手术'
        },
        {
          id: '4',
          serviceType: '麻醉服务',
          code: 'ANESTHESIA001',
          nationalCode: '4533000000400\n00-ANESTHESIA001',
          name: '麻醉费',
          description: '麻醉服务费用',
          unit: '次',
          price: '300.00',
          department: '麻醉科',
          insuranceRatio: 80,
          exceptionContent: '',
          remark: '全身麻醉'
        },
        {
          id: '5',
          serviceType: '护理服务',
          code: 'NURSE002',
          nationalCode: '4533000000500\n00-NURSE002',
          name: '特级护理费',
          description: '特级护理服务费用',
          unit: '天',
          price: '120.00',
          department: '护理部',
          insuranceRatio: 95,
          exceptionContent: '',
          remark: '特级护理'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 5,
      loading: false,
      detailDialogVisible: false,
      selectedService: null
    }
  },
  methods: {
    searchServices() {
      this.loading = true;
      setTimeout(() => {
        console.log('搜索条件:', this.searchForm);
        // 这里应该调用API进行搜索
        this.loading = false;
        this.$message.success('查询完成');
      }, 1000);
    },
    
    resetSearch() {
      this.searchForm = {
        name: '',
        serviceType: '',
        minPrice: '',
        maxPrice: ''
      };
      this.searchServices();
    },
    
    viewDetail(row) {
      this.selectedService = row;
      this.detailDialogVisible = true;
    },
    
    getServiceType(serviceType) {
      const typeMap = {
        '护理服务': 'primary',
        '住院服务': 'success',
        '手术服务': 'danger',
        '麻醉服务': 'warning'
      };
      return typeMap[serviceType] || 'info';
    },
    
    handleSizeChange(val) {
      this.pageSize = val;
      this.searchServices();
    },
    
    handleCurrentChange(val) {
      this.currentPage = val;
      this.searchServices();
    }
  },
  
  mounted() {
    this.searchServices();
  }
}
</script>

<style scoped>
.service-query {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  font-size: 24px;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
