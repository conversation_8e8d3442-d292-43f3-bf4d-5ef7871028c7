<template>
  <div class="multi-person-drug-query">
    <div class="page-header">
      <h2>多个人员用药信息查询</h2>
    </div>
    
    <div class="search-section">
      <el-form :inline="true" class="search-form">
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="身份证号">
          <el-input v-model="searchForm.idCard" placeholder="请输入身份证号" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-section">
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="index" label="序号" width="80" align="center"></el-table-column>
        <el-table-column prop="name" label="姓名" width="120"></el-table-column>
        <el-table-column prop="gender" label="性别" width="80"></el-table-column>
        <el-table-column prop="idCard" label="身份证号" width="180"></el-table-column>
        <el-table-column prop="drugName" label="药品名称" width="150"></el-table-column>
        <el-table-column prop="specification" label="规格" width="120"></el-table-column>
        <el-table-column prop="quantity" label="数量" width="100"></el-table-column>
        <el-table-column prop="unit" label="单位" width="80"></el-table-column>
        <el-table-column prop="unitPrice" label="单价" width="100"></el-table-column>
        <el-table-column prop="totalAmount" label="总金额" width="120"></el-table-column>
        <el-table-column prop="reimbursementRatio" label="报销比例" width="120"></el-table-column>
        <el-table-column prop="reimbursementAmount" label="报销金额" width="120"></el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="info" @click="handleEdit(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'MultiPersonDrugQuery',
  data() {
    return {
      searchForm: {
        name: '',
        idCard: ''
      },
      tableData: [
        {
          index: 1,
          name: '张三',
          gender: '男',
          idCard: '110101199001011234',
          drugName: '阿莫西林胶囊',
          specification: '0.25g*24粒',
          quantity: 2,
          unit: '盒',
          unitPrice: 15.50,
          totalAmount: 31.00,
          reimbursementRatio: '80%',
          reimbursementAmount: 24.80
        },
        {
          index: 2,
          name: '李四',
          gender: '女',
          idCard: '110101199002021234',
          drugName: '感冒灵颗粒',
          specification: '10g*12袋',
          quantity: 1,
          unit: '盒',
          unitPrice: 28.00,
          totalAmount: 28.00,
          reimbursementRatio: '70%',
          reimbursementAmount: 19.60
        },
        {
          index: 3,
          name: '王五',
          gender: '男',
          idCard: '110101199003031234',
          drugName: '布洛芬缓释胶囊',
          specification: '0.3g*20粒',
          quantity: 1,
          unit: '盒',
          unitPrice: 22.50,
          totalAmount: 22.50,
          reimbursementRatio: '80%',
          reimbursementAmount: 18.00
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 3
    }
  },
  methods: {
    handleSearch() {
      console.log('搜索条件:', this.searchForm);
      // 这里添加搜索逻辑
    },
    handleReset() {
      this.searchForm = {
        name: '',
        idCard: ''
      };
    },
    handleView(row) {
      console.log('查看:', row);
      // 这里添加查看详情逻辑
    },
    handleEdit(row) {
      console.log('编辑:', row);
      // 这里添加编辑逻辑
    },
    handleSizeChange(val) {
      this.pageSize = val;
      // 重新加载数据
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      // 重新加载数据
    }
  }
}
</script>

<style scoped>
.multi-person-drug-query {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #333;
  font-size: 24px;
  margin: 0;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.table-section {
  background: white;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-section {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
