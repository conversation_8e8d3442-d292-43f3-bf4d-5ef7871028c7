<template>
  <div class="drug-query">
    <div class="page-header">
      <h2>药品信息查询</h2>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="药品名称">
          <el-input v-model="searchForm.name" placeholder="请输入药品名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="药品类型">
          <el-select v-model="searchForm.type" placeholder="请选择药品类型" clearable>
            <el-option label="甲类" value="甲类"></el-option>
            <el-option label="乙类" value="乙类"></el-option>
            <el-option label="丙类" value="丙类"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="生产厂家">
          <el-input v-model="searchForm.manufacturer" placeholder="请输入生产厂家" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchDrugs">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 结果表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>查询结果</span>
          <span class="result-count">共找到 {{ total }} 条记录</span>
        </div>
      </template>
      
      <el-table 
        :data="tableData" 
        border 
        style="width: 100%"
        v-loading="loading">
        <el-table-column prop="id" label="序号" width="80" align="center"></el-table-column>
        <el-table-column prop="type" label="药品类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.type === '甲类' ? 'success' : row.type === '乙类' ? 'warning' : 'info'">
              {{ row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="药品名称" min-width="150"></el-table-column>
        <el-table-column prop="brand" label="品名" min-width="120"></el-table-column>
        <el-table-column prop="spec" label="规格" min-width="200"></el-table-column>
        <el-table-column prop="unit" label="单位" width="80" align="center"></el-table-column>
        <el-table-column prop="price" label="价格(元)" width="100" align="center">
          <template #default="{ row }">
            <span style="color: #E6A23C; font-weight: bold;">¥{{ row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="manufacturer" label="生产厂家" min-width="200"></el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button type="text" @click="viewDetail(row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </el-card>
    
    <!-- 详情弹窗 -->
    <el-dialog title="药品详情" v-model="detailDialogVisible" width="50%">
      <el-descriptions :column="2" border v-if="selectedDrug">
        <el-descriptions-item label="药品类型">{{ selectedDrug.type }}</el-descriptions-item>
        <el-descriptions-item label="药品名称">{{ selectedDrug.name }}</el-descriptions-item>
        <el-descriptions-item label="品名">{{ selectedDrug.brand }}</el-descriptions-item>
        <el-descriptions-item label="规格">{{ selectedDrug.spec }}</el-descriptions-item>
        <el-descriptions-item label="单位">{{ selectedDrug.unit }}</el-descriptions-item>
        <el-descriptions-item label="价格">¥{{ selectedDrug.price }}</el-descriptions-item>
        <el-descriptions-item label="生产厂家" :span="2">{{ selectedDrug.manufacturer }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ selectedDrug.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DrugQuery',
  data() {
    return {
      searchForm: {
        name: '',
        type: '',
        manufacturer: ''
      },
      tableData: [
        {
          id: '1',
          type: '甲类',
          name: '阿司匹林',
          brand: '拜阿司匹灵',
          spec: '100mg*30片',
          unit: '盒',
          price: '15.80',
          manufacturer: '拜耳医药保健有限公司',
          remark: '用于预防心脑血管疾病'
        },
        {
          id: '2',
          type: '乙类',
          name: '奥美拉唑',
          brand: '洛赛克',
          spec: '20mg*14粒',
          unit: '盒',
          price: '45.60',
          manufacturer: '阿斯利康制药有限公司',
          remark: '用于治疗胃溃疡'
        },
        {
          id: '3',
          type: '丙类',
          name: '奥比帕利',
          brand: '维建乐',
          spec: '奥比他韦12.5mg,帕立瑞韦75mg,利托那韦50mg',
          unit: '盒',
          price: '2712.60',
          manufacturer: '爱尔兰Fournier Laboratories Ireland Limited',
          remark: '用于治疗慢性丙型肝炎'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 3,
      loading: false,
      detailDialogVisible: false,
      selectedDrug: null
    }
  },
  methods: {
    searchDrugs() {
      this.loading = true;
      // 模拟搜索延迟
      setTimeout(() => {
        console.log('搜索条件:', this.searchForm);
        // 这里应该调用API进行搜索
        this.loading = false;
        this.$message.success('查询完成');
      }, 1000);
    },
    
    resetSearch() {
      this.searchForm = {
        name: '',
        type: '',
        manufacturer: ''
      };
      this.searchDrugs();
    },
    
    viewDetail(row) {
      this.selectedDrug = row;
      this.detailDialogVisible = true;
    },
    
    handleSizeChange(val) {
      this.pageSize = val;
      this.searchDrugs();
    },
    
    handleCurrentChange(val) {
      this.currentPage = val;
      this.searchDrugs();
    }
  },
  
  mounted() {
    this.searchDrugs();
  }
}
</script>

<style scoped>
.drug-query {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  font-size: 24px;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-count {
  color: #909399;
  font-size: 14px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.el-form-item {
  margin-bottom: 0;
}
</style>
