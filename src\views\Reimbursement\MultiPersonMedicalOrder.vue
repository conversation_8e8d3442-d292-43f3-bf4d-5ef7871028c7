<template>
  <div class="multi-person-medical-order">
    <div class="page-header">
      <h2>多个人员医嘱</h2>
    </div>
    
    <div class="search-section">
      <el-form :inline="true" class="search-form">
        <el-form-item label="患者姓名">
          <el-input v-model="searchForm.patientName" placeholder="请输入患者姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="医嘱类型">
          <el-select v-model="searchForm.orderType" placeholder="请选择医嘱类型" clearable>
            <el-option label="药品医嘱" value="drug"></el-option>
            <el-option label="诊疗医嘱" value="treatment"></el-option>
            <el-option label="检查医嘱" value="examination"></el-option>
            <el-option label="护理医嘱" value="nursing"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="医嘱状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待执行" value="pending"></el-option>
            <el-option label="执行中" value="executing"></el-option>
            <el-option label="已完成" value="completed"></el-option>
            <el-option label="已取消" value="cancelled"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="content-layout">
      <div class="left-panel">
        <div class="patient-list">
          <div class="list-header">
            <h3>患者列表</h3>
            <el-input 
              v-model="patientFilter" 
              placeholder="搜索患者" 
              size="small"
              prefix-icon="el-icon-search">
            </el-input>
          </div>
          <div class="patient-items">
            <div 
              v-for="patient in filteredPatients" 
              :key="patient.id"
              :class="['patient-item', { active: selectedPatient?.id === patient.id }]"
              @click="selectPatient(patient)">
              <div class="patient-info">
                <div class="patient-name">{{ patient.name }}</div>
                <div class="patient-id">{{ patient.idCard }}</div>
                <div class="patient-room">{{ patient.roomNumber }}</div>
              </div>
              <div class="order-count">
                <el-badge :value="patient.orderCount" type="primary"></el-badge>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="right-panel">
        <div class="order-details" v-if="selectedPatient">
          <div class="details-header">
            <h3>{{ selectedPatient.name }} - 医嘱详情</h3>
            <div class="header-actions">
              <el-button size="small" type="primary" @click="handleAddOrder">新增医嘱</el-button>
              <el-button size="small" type="success" @click="handleBatchExecute">批量执行</el-button>
            </div>
          </div>
          
          <el-table 
            :data="currentOrders" 
            border 
            style="width: 100%"
            @selection-change="handleSelectionChange"
            max-height="400">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="orderNo" label="医嘱编号" width="120"></el-table-column>
            <el-table-column prop="orderType" label="医嘱类型" width="100"></el-table-column>
            <el-table-column prop="content" label="医嘱内容" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column prop="dosage" label="用法用量" width="150"></el-table-column>
            <el-table-column prop="frequency" label="频次" width="100"></el-table-column>
            <el-table-column prop="duration" label="疗程" width="100"></el-table-column>
            <el-table-column prop="doctor" label="开具医生" width="100"></el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="开具时间" width="150"></el-table-column>
            <el-table-column label="操作" width="180" fixed="right">
              <template #default="scope">
                <el-button size="small" type="primary" @click="handleView(scope.row)">查看</el-button>
                <el-button size="small" type="success" @click="handleExecute(scope.row)" 
                           :disabled="scope.row.status === '已完成'">执行</el-button>
                <el-button size="small" type="danger" @click="handleCancel(scope.row)"
                           :disabled="scope.row.status === '已完成'">取消</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <div class="empty-state" v-else>
          <div class="empty-content">
            <i class="el-icon-document"></i>
            <p>请选择患者查看医嘱详情</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MultiPersonMedicalOrder',
  data() {
    return {
      searchForm: {
        patientName: '',
        orderType: '',
        status: ''
      },
      patientFilter: '',
      selectedPatient: null,
      selectedOrders: [],
      patients: [
        {
          id: 1,
          name: '张三',
          idCard: '110101199001011234',
          roomNumber: '301-1',
          orderCount: 5
        },
        {
          id: 2,
          name: '李四',
          idCard: '110101199002021234',
          roomNumber: '302-2',
          orderCount: 3
        },
        {
          id: 3,
          name: '王五',
          idCard: '110101199003031234',
          roomNumber: '303-1',
          orderCount: 7
        }
      ],
      orders: {
        1: [
          {
            orderNo: 'YZ001',
            orderType: '药品医嘱',
            content: '阿莫西林胶囊 0.25g',
            dosage: '口服',
            frequency: '每日3次',
            duration: '7天',
            doctor: '张医生',
            status: '待执行',
            createTime: '2024-01-15 10:30:00'
          },
          {
            orderNo: 'YZ002',
            orderType: '检查医嘱',
            content: '血常规检查',
            dosage: '-',
            frequency: '一次性',
            duration: '-',
            doctor: '李医生',
            status: '已完成',
            createTime: '2024-01-14 14:20:00'
          }
        ],
        2: [
          {
            orderNo: 'YZ003',
            orderType: '诊疗医嘱',
            content: '内科门诊诊查',
            dosage: '-',
            frequency: '一次性',
            duration: '-',
            doctor: '王医生',
            status: '执行中',
            createTime: '2024-01-13 09:15:00'
          }
        ],
        3: [
          {
            orderNo: 'YZ004',
            orderType: '药品医嘱',
            content: '布洛芬缓释胶囊 0.3g',
            dosage: '口服',
            frequency: '每日2次',
            duration: '5天',
            doctor: '赵医生',
            status: '待执行',
            createTime: '2024-01-12 16:45:00'
          }
        ]
      }
    }
  },
  computed: {
    filteredPatients() {
      if (!this.patientFilter) return this.patients;
      return this.patients.filter(patient => 
        patient.name.includes(this.patientFilter) || 
        patient.idCard.includes(this.patientFilter)
      );
    },
    currentOrders() {
      return this.selectedPatient ? (this.orders[this.selectedPatient.id] || []) : [];
    }
  },
  methods: {
    handleSearch() {
      console.log('搜索条件:', this.searchForm);
    },
    handleReset() {
      this.searchForm = {
        patientName: '',
        orderType: '',
        status: ''
      };
    },
    selectPatient(patient) {
      this.selectedPatient = patient;
    },
    handleAddOrder() {
      console.log('新增医嘱');
    },
    handleBatchExecute() {
      console.log('批量执行医嘱:', this.selectedOrders);
    },
    handleSelectionChange(selection) {
      this.selectedOrders = selection;
    },
    handleView(row) {
      console.log('查看医嘱:', row);
    },
    handleExecute(row) {
      console.log('执行医嘱:', row);
    },
    handleCancel(row) {
      console.log('取消医嘱:', row);
    },
    getStatusType(status) {
      const statusMap = {
        '待执行': 'warning',
        '执行中': 'info',
        '已完成': 'success',
        '已取消': 'danger'
      };
      return statusMap[status] || 'info';
    }
  }
}
</script>

<style scoped>
.multi-person-medical-order {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #333;
  font-size: 24px;
  margin: 0;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.content-layout {
  display: flex;
  gap: 20px;
  height: 600px;
}

.left-panel {
  width: 300px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.patient-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.list-header h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.patient-items {
  flex: 1;
  overflow-y: auto;
}

.patient-item {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s;
}

.patient-item:hover {
  background-color: #f5f5f5;
}

.patient-item.active {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.patient-info {
  flex: 1;
}

.patient-name {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.patient-id {
  font-size: 12px;
  color: #666;
  margin-bottom: 3px;
}

.patient-room {
  font-size: 12px;
  color: #999;
}

.right-panel {
  flex: 1;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 20px;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.details-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content {
  text-align: center;
  color: #999;
}

.empty-content i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.empty-content p {
  margin: 0;
  font-size: 14px;
}
</style>
