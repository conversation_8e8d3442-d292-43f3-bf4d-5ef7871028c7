<template>
  <div class="multi-person-drug-stats">
    <div class="page-header">
      <h2>多个人员用药信息统计</h2>
    </div>
    
    <div class="filter-section">
      <el-form :inline="true" class="filter-form">
        <el-form-item label="统计类型">
          <el-select v-model="filterForm.statsType" placeholder="请选择统计类型">
            <el-option label="按药品分类" value="category"></el-option>
            <el-option label="按费用区间" value="cost"></el-option>
            <el-option label="按报销比例" value="ratio"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">筛选</el-button>
          <el-button @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="stats-content">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-card">
            <div class="card-header">
              <h3>药品使用分布</h3>
            </div>
            <div class="chart-container">
              <div ref="pieChart" class="chart"></div>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-card">
            <div class="card-header">
              <h3>报销金额分布</h3>
            </div>
            <div class="chart-container">
              <div ref="doughnutChart" class="chart"></div>
            </div>
          </div>
        </el-col>
      </el-row>

      <div class="summary-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="summary-card">
              <div class="summary-title">总用药人数</div>
              <div class="summary-value">{{ summaryData.totalPersons }}</div>
              <div class="summary-unit">人</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-card">
              <div class="summary-title">总用药金额</div>
              <div class="summary-value">{{ summaryData.totalAmount }}</div>
              <div class="summary-unit">元</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-card">
              <div class="summary-title">总报销金额</div>
              <div class="summary-value">{{ summaryData.totalReimbursement }}</div>
              <div class="summary-unit">元</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-card">
              <div class="summary-title">平均报销比例</div>
              <div class="summary-value">{{ summaryData.avgRatio }}</div>
              <div class="summary-unit">%</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="detail-table">
        <div class="table-header">
          <h3>详细统计数据</h3>
        </div>
        <el-table :data="detailData" border style="width: 100%">
          <el-table-column prop="category" label="药品分类" width="150"></el-table-column>
          <el-table-column prop="personCount" label="用药人数" width="120"></el-table-column>
          <el-table-column prop="drugCount" label="药品种类" width="120"></el-table-column>
          <el-table-column prop="totalAmount" label="总金额" width="150"></el-table-column>
          <el-table-column prop="reimbursementAmount" label="报销金额" width="150"></el-table-column>
          <el-table-column prop="ratio" label="报销比例" width="120"></el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button size="small" type="primary" @click="handleDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'MultiPersonDrugStats',
  data() {
    return {
      filterForm: {
        statsType: 'category',
        dateRange: []
      },
      summaryData: {
        totalPersons: 156,
        totalAmount: '45,678.90',
        totalReimbursement: '36,543.12',
        avgRatio: '80.0'
      },
      detailData: [
        {
          category: '抗生素类',
          personCount: 45,
          drugCount: 12,
          totalAmount: '12,345.67',
          reimbursementAmount: '9,876.54',
          ratio: '80%'
        },
        {
          category: '感冒药类',
          personCount: 38,
          drugCount: 8,
          totalAmount: '8,765.43',
          reimbursementAmount: '6,132.80',
          ratio: '70%'
        },
        {
          category: '消炎药类',
          personCount: 32,
          drugCount: 10,
          totalAmount: '15,432.10',
          reimbursementAmount: '12,345.68',
          ratio: '80%'
        },
        {
          category: '维生素类',
          personCount: 28,
          drugCount: 6,
          totalAmount: '5,678.90',
          reimbursementAmount: '3,975.23',
          ratio: '70%'
        }
      ]
    }
  },
  mounted() {
    this.initCharts();
    window.addEventListener('resize', this.handleResize);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize);
    if (this.pieChartInstance) {
      this.pieChartInstance.dispose();
    }
    if (this.doughnutChartInstance) {
      this.doughnutChartInstance.dispose();
    }
  },
  methods: {
    handleFilter() {
      console.log('筛选条件:', this.filterForm);
      // 这里添加筛选逻辑
    },
    handleExport() {
      console.log('导出统计数据');
      // 这里添加导出逻辑
    },
    handleDetail(row) {
      console.log('查看详情:', row);
      // 这里添加查看详情逻辑
    },
    initCharts() {
      this.initPieChart();
      this.initDoughnutChart();
    },
    initPieChart() {
      this.pieChartInstance = echarts.init(this.$refs.pieChart);
      const option = {
        title: {
          text: '药品使用分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['抗生素类', '感冒药类', '消炎药类', '维生素类', '其他']
        },
        series: [
          {
            name: '药品分类',
            type: 'pie',
            radius: '50%',
            data: [
              { value: 45, name: '抗生素类' },
              { value: 38, name: '感冒药类' },
              { value: 32, name: '消炎药类' },
              { value: 28, name: '维生素类' },
              { value: 13, name: '其他' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
      this.pieChartInstance.setOption(option);
    },
    initDoughnutChart() {
      this.doughnutChartInstance = echarts.init(this.$refs.doughnutChart);
      const option = {
        title: {
          text: '报销金额分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['已报销', '待报销', '个人支付']
        },
        series: [
          {
            name: '报销状态',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 36543.12, name: '已报销' },
              { value: 9135.78, name: '待报销' },
              { value: 8976.45, name: '个人支付' }
            ]
          }
        ]
      };
      this.doughnutChartInstance.setOption(option);
    },
    handleResize() {
      if (this.pieChartInstance) {
        this.pieChartInstance.resize();
      }
      if (this.doughnutChartInstance) {
        this.doughnutChartInstance.resize();
      }
    }
  }
}
</script>

<style scoped>
.multi-person-drug-stats {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #333;
  font-size: 24px;
  margin: 0;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-content {
  margin-top: 20px;
}

.chart-card {
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.card-header {
  padding: 20px 20px 0;
  border-bottom: 1px solid #eee;
}

.card-header h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.chart-container {
  padding: 20px;
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.summary-section {
  margin: 20px 0;
}

.summary-card {
  background: white;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.summary-title {
  color: #666;
  font-size: 14px;
  margin-bottom: 10px;
}

.summary-value {
  color: #333;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.summary-unit {
  color: #999;
  font-size: 12px;
}

.detail-table {
  background: white;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-header {
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}
</style>
