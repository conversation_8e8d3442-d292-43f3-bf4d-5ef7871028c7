import { createRouter, createWebHistory } from 'vue-router';
import Login from '../views/Login.vue';
import Register from '../views/Register.vue';
import AdmissionRegistration from '../views/AdmissionRegistration.vue';
import DrugQuery from '../views/DrugQuery.vue';
import DrugMaintenance from '../views/Information/DrugMaintenance.vue';
import TreatmentMaintenance from '../views/Information/TreatmentMaintenance.vue';
import MedicalServiceMaintenance from '../views/Information/MedicalServiceMaintenance.vue';
import DrugReimbursementRatio from '../views/Ratio/DrugReimbursementRatio.vue';
import FirstLevelHospitalRatio from '../views/Ratio/FirstLevelHospitalRatio.vue';
import SecondLevelHospitalRatio from '../views/Ratio/SecondLevelHospitalRatio.vue';
import ThirdLevelHospitalRatio from '../views/Ratio/ThirdLevelHospitalRatio.vue';
// 患者医嘱模块
import PatientDrugOrder from '../views/PatientOrder/PatientDrugOrder.vue';
import PatientTreatmentOrder from '../views/PatientOrder/PatientTreatmentOrder.vue';
import PatientServiceOrder from '../views/PatientOrder/PatientServiceOrder.vue';
// 患者诊断模块
import PatientDrugCost from '../views/PatientDiagnosis/PatientDrugCost.vue';
import PatientTreatmentCost from '../views/PatientDiagnosis/PatientTreatmentCost.vue';
// 信息查询模块
import TreatmentQuery from '../views/Query/TreatmentQuery.vue';
import ServiceQuery from '../views/Query/ServiceQuery.vue';
// 报销管理模块
import MultiPersonDrugQuery from '../views/Reimbursement/MultiPersonDrugQuery.vue';
import MultiPersonDrugStats from '../views/Reimbursement/MultiPersonDrugStats.vue';
import MultiPersonCostDetail from '../views/Reimbursement/MultiPersonCostDetail.vue';
import MultiPersonMedicalOrder from '../views/Reimbursement/MultiPersonMedicalOrder.vue';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/register',
    name: 'Register',
    component: Register
  },
  {
    path: '/admissionRegistration',
    name: 'AdmissionRegistration',
    component: AdmissionRegistration
  },
  {
    path: '/drugQuery',
    name: 'DrugQuery',
    component: DrugQuery
  },
  {
    path: '/drugMaintenance',
    name: 'DrugMaintenance',
    component: DrugMaintenance
  },
  {
    path: '/treatmentMaintenance',
    name: 'TreatmentMaintenance',
    component: TreatmentMaintenance
  },
  {
    path: '/medicalServiceMaintenance',
    name: 'MedicalServiceMaintenance',
    component: MedicalServiceMaintenance
  },
  {
    path: '/drugReimbursementRatio',
    name: 'DrugReimbursementRatio',
    component: DrugReimbursementRatio
  },
  {
    path:'/firstLevelHospitalRatio',
    name:'FirstLevelHospitalRatio',
    component: FirstLevelHospitalRatio
  },
  {
    path: '/secondLevelHospitalRatio',
    name:'SecondLevelHospitalRatio',
    component: SecondLevelHospitalRatio
  },
  {
    path: '/thirdLevelHospitalRatio',
    name:'ThirdLevelHospitalRatio',
    component: ThirdLevelHospitalRatio
  },
  // 患者医嘱模块路由
  {
    path: '/patientDrugOrder',
    name: 'PatientDrugOrder',
    component: PatientDrugOrder
  },
  {
    path: '/patientTreatmentOrder',
    name: 'PatientTreatmentOrder',
    component: PatientTreatmentOrder
  },
  {
    path: '/patientServiceOrder',
    name: 'PatientServiceOrder',
    component: PatientServiceOrder
  },
  // 患者诊断模块路由
  {
    path: '/patientDrugCost',
    name: 'PatientDrugCost',
    component: PatientDrugCost
  },
  {
    path: '/patientTreatmentCost',
    name: 'PatientTreatmentCost',
    component: PatientTreatmentCost
  },
  // 信息查询模块路由
  {
    path: '/treatmentQuery',
    name: 'TreatmentQuery',
    component: TreatmentQuery
  },
  {
    path: '/serviceQuery',
    name: 'ServiceQuery',
    component: ServiceQuery
  },
  // 报销管理模块路由
  {
    path: '/multiPersonDrugQuery',
    name: 'MultiPersonDrugQuery',
    component: MultiPersonDrugQuery
  },
  {
    path: '/multiPersonDrugStats',
    name: 'MultiPersonDrugStats',
    component: MultiPersonDrugStats
  },
  {
    path: '/multiPersonCostDetail',
    name: 'MultiPersonCostDetail',
    component: MultiPersonCostDetail
  },
  {
    path: '/multiPersonMedicalOrder',
    name: 'MultiPersonMedicalOrder',
    component: MultiPersonMedicalOrder
  },
  {
    path: '/',
    redirect: '/admissionRegistration' // 默认重定向到入院登记页面
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

export default router;