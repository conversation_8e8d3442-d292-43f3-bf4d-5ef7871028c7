<template>
  <div class="patient-drug-order">
    <div class="page-header">
      <h2>药品医嘱信息维护</h2>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="患者姓名">
          <el-input v-model="searchForm.patientName" placeholder="请输入患者姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="医嘱状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="待执行" value="pending"></el-option>
            <el-option label="执行中" value="executing"></el-option>
            <el-option label="已完成" value="completed"></el-option>
            <el-option label="已停止" value="stopped"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchOrders">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="addOrder">新增医嘱</el-button>
      <el-button type="success" @click="batchExecute">批量执行</el-button>
      <el-button type="warning" @click="batchStop">批量停止</el-button>
      <el-button type="danger" @click="deleteSelected">删除选中</el-button>
    </div>

    <!-- 数据表格 -->
    <el-table 
      :data="tableData" 
      border 
      style="width: 100%"
      v-loading="loading"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"></el-table-column>
      <el-table-column prop="id" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="patientName" label="患者姓名" width="100"></el-table-column>
      <el-table-column prop="patientId" label="患者ID" width="120"></el-table-column>
      <el-table-column prop="drugName" label="药品名称" min-width="150"></el-table-column>
      <el-table-column prop="specification" label="规格" width="120"></el-table-column>
      <el-table-column prop="dosage" label="剂量" width="80" align="center"></el-table-column>
      <el-table-column prop="frequency" label="频次" width="100" align="center"></el-table-column>
      <el-table-column prop="route" label="给药途径" width="100" align="center"></el-table-column>
      <el-table-column prop="duration" label="疗程" width="80" align="center"></el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag 
            :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="开嘱时间" width="150" align="center"></el-table-column>
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="editOrder(row)">修改</el-button>
          <el-button size="small" type="success" @click="executeOrder(row)" v-if="row.status === 'pending'">执行</el-button>
          <el-button size="small" type="warning" @click="stopOrder(row)" v-if="row.status === 'executing'">停止</el-button>
          <el-button size="small" type="danger" @click="deleteOrder(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div>

    <!-- 新增/编辑医嘱弹窗 -->
    <el-dialog 
      :title="dialogTitle" 
      v-model="dialogVisible" 
      width="60%"
      :before-close="handleClose">
      <el-form :model="orderForm" :rules="rules" ref="orderFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="患者姓名" prop="patientName">
              <el-input v-model="orderForm.patientName" placeholder="请输入患者姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="患者ID" prop="patientId">
              <el-input v-model="orderForm.patientId" placeholder="请输入患者ID"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="药品名称" prop="drugName">
              <el-select v-model="orderForm.drugName" placeholder="请选择药品" filterable>
                <el-option label="阿司匹林" value="阿司匹林"></el-option>
                <el-option label="奥美拉唑" value="奥美拉唑"></el-option>
                <el-option label="头孢克肟" value="头孢克肟"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格" prop="specification">
              <el-input v-model="orderForm.specification" placeholder="请输入规格"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="剂量" prop="dosage">
              <el-input v-model="orderForm.dosage" placeholder="剂量"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="频次" prop="frequency">
              <el-select v-model="orderForm.frequency" placeholder="请选择频次">
                <el-option label="每日一次" value="qd"></el-option>
                <el-option label="每日两次" value="bid"></el-option>
                <el-option label="每日三次" value="tid"></el-option>
                <el-option label="每日四次" value="qid"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="给药途径" prop="route">
              <el-select v-model="orderForm.route" placeholder="请选择给药途径">
                <el-option label="口服" value="po"></el-option>
                <el-option label="静脉注射" value="iv"></el-option>
                <el-option label="肌肉注射" value="im"></el-option>
                <el-option label="外用" value="topical"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="疗程" prop="duration">
              <el-input v-model="orderForm.duration" placeholder="请输入疗程"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="orderForm.status" placeholder="请选择状态">
                <el-option label="待执行" value="pending"></el-option>
                <el-option label="执行中" value="executing"></el-option>
                <el-option label="已完成" value="completed"></el-option>
                <el-option label="已停止" value="stopped"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input v-model="orderForm.remark" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveOrder">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PatientDrugOrder',
  data() {
    return {
      searchForm: {
        patientName: '',
        status: ''
      },
      tableData: [
        {
          id: '1',
          patientName: '张三',
          patientId: 'P001',
          drugName: '阿司匹林',
          specification: '100mg*30片',
          dosage: '100mg',
          frequency: 'qd',
          route: 'po',
          duration: '7天',
          status: 'executing',
          createTime: '2024-01-15 09:30:00',
          remark: '饭后服用'
        },
        {
          id: '2',
          patientName: '李四',
          patientId: 'P002',
          drugName: '奥美拉唑',
          specification: '20mg*14粒',
          dosage: '20mg',
          frequency: 'bid',
          route: 'po',
          duration: '14天',
          status: 'pending',
          createTime: '2024-01-15 10:15:00',
          remark: '空腹服用'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 2,
      loading: false,
      multipleSelection: [],
      
      // 弹窗相关
      dialogVisible: false,
      dialogTitle: '新增医嘱',
      orderForm: {
        patientName: '',
        patientId: '',
        drugName: '',
        specification: '',
        dosage: '',
        frequency: '',
        route: '',
        duration: '',
        status: 'pending',
        remark: ''
      },
      rules: {
        patientName: [{ required: true, message: '请输入患者姓名', trigger: 'blur' }],
        patientId: [{ required: true, message: '请输入患者ID', trigger: 'blur' }],
        drugName: [{ required: true, message: '请选择药品', trigger: 'change' }],
        dosage: [{ required: true, message: '请输入剂量', trigger: 'blur' }],
        frequency: [{ required: true, message: '请选择频次', trigger: 'change' }],
        route: [{ required: true, message: '请选择给药途径', trigger: 'change' }]
      }
    }
  },
  methods: {
    searchOrders() {
      this.loading = true;
      setTimeout(() => {
        console.log('搜索条件:', this.searchForm);
        this.loading = false;
        this.$message.success('查询完成');
      }, 1000);
    },
    
    resetSearch() {
      this.searchForm = {
        patientName: '',
        status: ''
      };
      this.searchOrders();
    },
    
    addOrder() {
      this.dialogTitle = '新增医嘱';
      this.orderForm = {
        patientName: '',
        patientId: '',
        drugName: '',
        specification: '',
        dosage: '',
        frequency: '',
        route: '',
        duration: '',
        status: 'pending',
        remark: ''
      };
      this.dialogVisible = true;
    },
    
    editOrder(row) {
      this.dialogTitle = '编辑医嘱';
      this.orderForm = { ...row };
      this.dialogVisible = true;
    },
    
    saveOrder() {
      this.$refs.orderFormRef.validate((valid) => {
        if (valid) {
          console.log('保存医嘱:', this.orderForm);
          this.dialogVisible = false;
          this.$message.success('保存成功');
        }
      });
    },
    
    executeOrder(row) {
      this.$confirm('确认执行该医嘱吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.status = 'executing';
        this.$message.success('医嘱已开始执行');
      });
    },
    
    stopOrder(row) {
      this.$confirm('确认停止该医嘱吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.status = 'stopped';
        this.$message.success('医嘱已停止');
      });
    },
    
    deleteOrder(row) {
      this.$confirm('确认删除该医嘱吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.tableData.indexOf(row);
        this.tableData.splice(index, 1);
        this.$message.success('删除成功');
      });
    },
    
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },
    
    batchExecute() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要执行的医嘱');
        return;
      }
      this.$confirm('确认批量执行选中的医嘱吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.multipleSelection.forEach(item => {
          if (item.status === 'pending') {
            item.status = 'executing';
          }
        });
        this.$message.success('批量执行成功');
      });
    },
    
    batchStop() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要停止的医嘱');
        return;
      }
      this.$confirm('确认批量停止选中的医嘱吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.multipleSelection.forEach(item => {
          if (item.status === 'executing') {
            item.status = 'stopped';
          }
        });
        this.$message.success('批量停止成功');
      });
    },
    
    deleteSelected() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要删除的医嘱');
        return;
      }
      this.$confirm('确认删除选中的医嘱吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.multipleSelection.forEach(item => {
          const index = this.tableData.indexOf(item);
          this.tableData.splice(index, 1);
        });
        this.$message.success('删除成功');
      });
    },
    
    getStatusType(status) {
      const statusMap = {
        'pending': 'info',
        'executing': 'success',
        'completed': 'primary',
        'stopped': 'danger'
      };
      return statusMap[status] || 'info';
    },
    
    getStatusText(status) {
      const statusMap = {
        'pending': '待执行',
        'executing': '执行中',
        'completed': '已完成',
        'stopped': '已停止'
      };
      return statusMap[status] || '未知';
    },
    
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => {});
    },
    
    handleSizeChange(val) {
      this.pageSize = val;
      this.searchOrders();
    },
    
    handleCurrentChange(val) {
      this.currentPage = val;
      this.searchOrders();
    }
  },
  
  mounted() {
    this.searchOrders();
  }
}
</script>

<style scoped>
.patient-drug-order {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  font-size: 24px;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
}

.action-buttons {
  margin-bottom: 20px;
}

.action-buttons .el-button {
  margin-right: 10px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>
