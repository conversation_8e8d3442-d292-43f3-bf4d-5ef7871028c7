<template>
  <div class="multi-person-cost-detail">
    <div class="page-header">
      <h2>多个人员费用明细</h2>
    </div>
    
    <div class="search-section">
      <el-form :inline="true" class="search-form">
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="费用类型">
          <el-select v-model="searchForm.costType" placeholder="请选择费用类型" clearable>
            <el-option label="药品费用" value="drug"></el-option>
            <el-option label="诊疗费用" value="treatment"></el-option>
            <el-option label="检查费用" value="examination"></el-option>
            <el-option label="住院费用" value="hospitalization"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleExport">导出明细</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="summary-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="summary-card">
            <div class="card-icon drug-icon">💊</div>
            <div class="card-content">
              <div class="card-title">药品费用</div>
              <div class="card-value">¥ {{ summaryData.drugCost }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-card">
            <div class="card-icon treatment-icon">🏥</div>
            <div class="card-content">
              <div class="card-title">诊疗费用</div>
              <div class="card-value">¥ {{ summaryData.treatmentCost }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-card">
            <div class="card-icon exam-icon">🔬</div>
            <div class="card-content">
              <div class="card-title">检查费用</div>
              <div class="card-value">¥ {{ summaryData.examCost }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-card">
            <div class="card-icon total-icon">💰</div>
            <div class="card-content">
              <div class="card-title">总费用</div>
              <div class="card-value">¥ {{ summaryData.totalCost }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="detail-section">
      <div class="section-header">
        <h3>费用明细列表</h3>
        <div class="header-actions">
          <el-button size="small" @click="handleBatchReimburse">批量报销</el-button>
        </div>
      </div>
      
      <el-table 
        :data="detailData" 
        border 
        style="width: 100%"
        @selection-change="handleSelectionChange"
        max-height="500">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="index" label="序号" width="80" align="center"></el-table-column>
        <el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
        <el-table-column prop="idCard" label="身份证号" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="costType" label="费用类型" width="100"></el-table-column>
        <el-table-column prop="itemName" label="项目名称" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="quantity" label="数量" width="80" align="center"></el-table-column>
        <el-table-column prop="unitPrice" label="单价" width="100" align="right"></el-table-column>
        <el-table-column prop="totalAmount" label="总金额" width="120" align="right"></el-table-column>
        <el-table-column prop="reimbursementRatio" label="报销比例" width="100" align="center"></el-table-column>
        <el-table-column prop="reimbursementAmount" label="报销金额" width="120" align="right"></el-table-column>
        <el-table-column prop="personalPayment" label="个人支付" width="120" align="right"></el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150"></el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="success" @click="handleReimburse(scope.row)" 
                       :disabled="scope.row.status === '已报销'">报销</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'MultiPersonCostDetail',
  data() {
    return {
      searchForm: {
        name: '',
        costType: '',
        dateRange: []
      },
      summaryData: {
        drugCost: '25,678.90',
        treatmentCost: '18,432.50',
        examCost: '12,345.60',
        totalCost: '56,456.00'
      },
      detailData: [
        {
          index: 1,
          patientName: '张三',
          idCard: '110101199001011234',
          costType: '药品费用',
          itemName: '阿莫西林胶囊',
          quantity: 2,
          unitPrice: '15.50',
          totalAmount: '31.00',
          reimbursementRatio: '80%',
          reimbursementAmount: '24.80',
          personalPayment: '6.20',
          status: '待报销',
          createTime: '2024-01-15 10:30:00'
        },
        {
          index: 2,
          patientName: '李四',
          idCard: '110101199002021234',
          costType: '诊疗费用',
          itemName: '内科门诊诊查费',
          quantity: 1,
          unitPrice: '50.00',
          totalAmount: '50.00',
          reimbursementRatio: '90%',
          reimbursementAmount: '45.00',
          personalPayment: '5.00',
          status: '已报销',
          createTime: '2024-01-14 14:20:00'
        },
        {
          index: 3,
          patientName: '王五',
          idCard: '110101199003031234',
          costType: '检查费用',
          itemName: '血常规检查',
          quantity: 1,
          unitPrice: '80.00',
          totalAmount: '80.00',
          reimbursementRatio: '70%',
          reimbursementAmount: '56.00',
          personalPayment: '24.00',
          status: '待报销',
          createTime: '2024-01-13 09:15:00'
        }
      ],
      selectedRows: [],
      currentPage: 1,
      pageSize: 10,
      total: 3
    }
  },
  methods: {
    handleSearch() {
      console.log('搜索条件:', this.searchForm);
    },
    handleReset() {
      this.searchForm = {
        name: '',
        costType: '',
        dateRange: []
      };
    },
    handleExport() {
      console.log('导出费用明细');
    },
    handleView(row) {
      console.log('查看详情:', row);
    },
    handleReimburse(row) {
      console.log('单个报销:', row);
    },
    handleBatchReimburse() {
      console.log('批量报销:', this.selectedRows);
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    getStatusType(status) {
      const statusMap = {
        '待报销': 'warning',
        '已报销': 'success',
        '报销中': 'info',
        '已拒绝': 'danger'
      };
      return statusMap[status] || 'info';
    },
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    }
  }
}
</script>

<style scoped>
.multi-person-cost-detail {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #333;
  font-size: 24px;
  margin: 0;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-cards {
  margin-bottom: 20px;
}

.summary-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
}

.card-icon {
  font-size: 32px;
  margin-right: 15px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drug-icon { background: #e3f2fd; }
.treatment-icon { background: #f3e5f5; }
.exam-icon { background: #e8f5e8; }
.total-icon { background: #fff3e0; }

.card-content {
  flex: 1;
}

.card-title {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.card-value {
  color: #333;
  font-size: 20px;
  font-weight: bold;
}

.detail-section {
  background: white;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
