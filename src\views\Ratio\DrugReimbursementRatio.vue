<template>
  <div class="drug-reimbursement">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="box-card">
          <div class="card-header">
            <span>药品保险报销等级列表</span>
            <el-button 
              type="primary" 
              size="small" 
              @click="addReimbursement"
              icon="el-icon-plus">
              + 新增
            </el-button>
          </div>
          <div>
            <el-table :data="reimbursementList" style="width: 100%">
              <el-table-column prop="level" label="等级" width="180"></el-table-column>
              <el-table-column prop="ratio" label="报销比例" width="180">
                <template #default="{ row }">
                  {{ row.ratio }}%
                </template>
              </el-table-column>
              <el-table-column label="状态" width="180">
                <template #default="{ row }">
                  <el-tag v-if="row.status === '启用'" type="success">启用</el-tag>
                  <el-tag v-else type="danger">停用</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="{ row }">
                  <el-button type="text" @click="editReimbursement(row)">编辑</el-button>
                  <el-button type="text" @click="deleteReimbursement(row)" style="color: #F56C6C;">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card">
          <div class="card-header">
            <span>详细信息</span>
            <el-tabs v-model="activeTab" @tab-click="handleTabChange">
              <el-tab-pane label="甲类" name="first"></el-tab-pane>
              <el-tab-pane label="乙类" name="second"></el-tab-pane>
              <el-tab-pane label="丙类" name="third"></el-tab-pane>
            </el-tabs>
          </div>
          <div>
            <el-form :model="form" label-width="80px" ref="reimbursementForm">
              <el-form-item 
                label="名称" 
                prop="name"
                :rules="[{ required: true, message: '请输入名称', trigger: 'blur' }]">
                <el-input v-model="form.name" placeholder="请输入报销等级名称"></el-input>
              </el-form-item>
              <el-form-item 
                label="报销比例" 
                prop="ratio"
                :rules="[{ required: true, message: '请输入报销比例', trigger: 'blur' }]">
                <el-input-number 
                  v-model="form.ratio" 
                  :min="0" 
                  :max="100"
                  :precision="0"
                  :controls="false"
                  placeholder="请输入报销比例">
                  <template #append>%</template>
                </el-input-number>
              </el-form-item>
              <el-form-item 
                label="状态" 
                prop="status"
                :rules="[{ required: true, message: '请选择状态', trigger: 'change' }]">
                <el-radio-group v-model="form.status">
                  <el-radio label="启用"></el-radio>
                  <el-radio label="停用"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="说明" prop="description">
                <el-input 
                  type="textarea" 
                  v-model="form.description" 
                  placeholder="请输入报销等级说明"
                  :rows="3"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveReimbursement">保存</el-button>
                <el-button @click="resetForm">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  data() {
    return {
      reimbursementList: [
        { id: 1, level: '甲类', ratio: 100, status: '启用', description: '全额报销药品' },
        { id: 2, level: '乙类', ratio: 80, status: '启用', description: '部分报销药品' },
        { id: 3, level: '丙类', ratio: 10, status: '启用', description: '少量报销药品' }
      ],
      form: {
        id: null,
        level: '甲类', // 默认等级
        name: '',
        ratio: null,
        status: '启用',
        description: ''
      },
      activeTab: 'first',
      isEditing: false
    };
  },
  watch: {
    activeTab(newVal) {
      // 当选项卡变化时，更新等级
      if (newVal === 'first') this.form.level = '甲类';
      else if (newVal === 'second') this.form.level = '乙类';
      else if (newVal === 'third') this.form.level = '丙类';
    }
  },
  methods: {
    addReimbursement() {
      // 重置表单并准备新增
      this.resetForm();
      this.isEditing = false;
      this.$message.success('请填写新的报销比例信息');
    },
    editReimbursement(row) {
      // 填充表单数据准备编辑
      this.form = { ...row };
      this.isEditing = true;
      
      // 根据等级设置激活的选项卡
      if (row.level === '甲类') this.activeTab = 'first';
      else if (row.level === '乙类') this.activeTab = 'second';
      else if (row.level === '丙类') this.activeTab = 'third';
      
      this.$message.success('正在编辑报销比例');
    },
    deleteReimbursement(row) {
      this.$confirm('确定要删除这条报销比例吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.reimbursementList = this.reimbursementList.filter(item => item.id !== row.id);
        this.$message.success('删除成功');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    saveReimbursement() {
      // 验证表单
      this.$refs.reimbursementForm.validate((valid) => {
        if (!valid) {
          this.$message.error('请填写完整信息');
          return false;
        }
        
        if (this.isEditing) {
          // 更新现有项
          const index = this.reimbursementList.findIndex(item => item.id === this.form.id);
          if (index !== -1) {
            this.reimbursementList.splice(index, 1, { ...this.form });
          }
        } else {
          // 添加新项
          const newId = this.reimbursementList.length > 0 
            ? Math.max(...this.reimbursementList.map(item => item.id)) + 1 
            : 1;
            
          this.reimbursementList.push({
            id: newId,
            ...this.form
          });
        }
        
        this.$message.success('保存成功');
        this.resetForm();
      });
    },
    resetForm() {
      this.$refs.reimbursementForm.resetFields();
      this.form = {
        id: null,
        level: '甲类',
        name: '',
        ratio: null,
        status: '启用',
        description: ''
      };
      this.activeTab = 'first';
      this.isEditing = false;
    },
    handleTabChange(tab) {
      // 当切换选项卡时，更新等级
      this.form.level = tab.props.label;
    }
  }
};
</script>

<style scoped>
.drug-reimbursement {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
  height: calc(100vh - 100px);
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-input-number {
  width: 100%;
}
</style>