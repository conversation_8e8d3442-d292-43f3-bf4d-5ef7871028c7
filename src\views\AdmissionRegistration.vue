<template>
  <div class="admission-registration">
    <div class="page-header">
      <h2>入院登记</h2>
    </div>
    
    <el-row :gutter="20">
      <!-- 基本信息 -->
      <el-col :span="12">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          
          <el-form :model="basicForm" label-width="100px" class="basic-form">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="姓名" required>
                  <el-input v-model="basicForm.name" placeholder="请输入姓名"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="身份证号" required>
                  <el-input v-model="basicForm.idCard" placeholder="请输入身份证号"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="年龄">
                  <el-input-number v-model="basicForm.age" :min="0" :max="150" placeholder="年龄"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="出生日期">
                  <el-date-picker
                    v-model="basicForm.birthDate"
                    type="date"
                    placeholder="请选择出生日期"
                    style="width: 100%">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="性别">
                  <el-select v-model="basicForm.gender" placeholder="请选择性别" style="width: 100%">
                    <el-option label="男" value="男"></el-option>
                    <el-option label="女" value="女"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职业">
                  <el-select v-model="basicForm.occupation" placeholder="请选择职业" style="width: 100%">
                    <el-option label="在职" value="在职"></el-option>
                    <el-option label="退休" value="退休"></el-option>
                    <el-option label="学生" value="学生"></el-option>
                    <el-option label="其他" value="其他"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="联系地址">
              <el-input v-model="basicForm.address" placeholder="请输入联系地址"></el-input>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <!-- 登记信息 -->
      <el-col :span="12">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>登记信息</span>
            </div>
          </template>
          
          <el-form :model="registrationForm" label-width="100px" class="registration-form">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="科室类型">
                  <el-select v-model="registrationForm.departmentType" placeholder="请选择科室类型" style="width: 100%">
                    <el-option label="内科" value="内科"></el-option>
                    <el-option label="外科" value="外科"></el-option>
                    <el-option label="妇科" value="妇科"></el-option>
                    <el-option label="儿科" value="儿科"></el-option>
                    <el-option label="急诊科" value="急诊科"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工作状态">
                  <el-select v-model="registrationForm.workStatus" placeholder="请选择工作状态" style="width: 100%">
                    <el-option label="在职" value="在职"></el-option>
                    <el-option label="退休" value="退休"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="入院时间">
                  <el-date-picker
                    v-model="registrationForm.admissionTime"
                    type="datetime"
                    placeholder="请选择入院时间"
                    style="width: 100%">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="医院">
                  <el-select v-model="registrationForm.hospital" placeholder="请选择医院" style="width: 100%">
                    <el-option label="一级医院" value="一级医院"></el-option>
                    <el-option label="二级医院" value="二级医院"></el-option>
                    <el-option label="三级医院" value="三级医院"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="saveRegistration">保存</el-button>
      <el-button @click="resetForm">重置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdmissionRegistration',
  data() {
    return {
      basicForm: {
        name: '',
        idCard: '',
        age: null,
        birthDate: '',
        gender: '',
        occupation: '',
        address: ''
      },
      registrationForm: {
        departmentType: '',
        workStatus: '',
        admissionTime: '',
        hospital: ''
      }
    }
  },
  methods: {
    saveRegistration() {
      // 验证表单
      if (!this.basicForm.name || !this.basicForm.idCard) {
        this.$message.error('请填写必填信息');
        return;
      }
      
      const registrationData = {
        ...this.basicForm,
        ...this.registrationForm,
        registrationTime: new Date().toISOString()
      };
      
      console.log('入院登记信息:', registrationData);
      this.$message.success('入院登记成功');
    },
    
    resetForm() {
      this.basicForm = {
        name: '',
        idCard: '',
        age: null,
        birthDate: '',
        gender: '',
        occupation: '',
        address: ''
      };
      this.registrationForm = {
        departmentType: '',
        workStatus: '',
        admissionTime: '',
        hospital: ''
      };
    }
  }
}
</script>

<style scoped>
.admission-registration {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  font-size: 24px;
  margin: 0;
}

.info-card {
  margin-bottom: 20px;
  height: 400px;
}

.card-header {
  font-weight: 600;
  color: #303133;
}

.basic-form,
.registration-form {
  padding: 10px 0;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
}

.action-buttons .el-button {
  margin: 0 10px;
  padding: 12px 30px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-input-number {
  width: 100%;
}
</style>
