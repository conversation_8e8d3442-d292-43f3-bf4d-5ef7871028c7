<template>
  <div class="patient-treatment-order">
    <div class="page-header">
      <h2>诊疗医嘱信息维护</h2>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="患者姓名">
          <el-input v-model="searchForm.patientName" placeholder="请输入患者姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="诊疗项目">
          <el-input v-model="searchForm.treatmentName" placeholder="请输入诊疗项目" clearable></el-input>
        </el-form-item>
        <el-form-item label="医嘱状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="待执行" value="pending"></el-option>
            <el-option label="执行中" value="executing"></el-option>
            <el-option label="已完成" value="completed"></el-option>
            <el-option label="已停止" value="stopped"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchOrders">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="addOrder">新增医嘱</el-button>
      <el-button type="success" @click="batchExecute">批量执行</el-button>
      <el-button type="warning" @click="batchStop">批量停止</el-button>
      <el-button type="danger" @click="deleteSelected">删除选中</el-button>
    </div>

    <!-- 数据表格 -->
    <el-table 
      :data="tableData" 
      border 
      style="width: 100%"
      v-loading="loading"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"></el-table-column>
      <el-table-column prop="id" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="patientName" label="患者姓名" width="100"></el-table-column>
      <el-table-column prop="patientId" label="患者ID" width="120"></el-table-column>
      <el-table-column prop="treatmentName" label="诊疗项目" min-width="150"></el-table-column>
      <el-table-column prop="treatmentCode" label="项目编码" width="120"></el-table-column>
      <el-table-column prop="quantity" label="数量" width="80" align="center"></el-table-column>
      <el-table-column prop="unit" label="单位" width="80" align="center"></el-table-column>
      <el-table-column prop="price" label="单价" width="100" align="center">
        <template #default="{ row }">
          ¥{{ row.price }}
        </template>
      </el-table-column>
      <el-table-column prop="totalAmount" label="总金额" width="100" align="center">
        <template #default="{ row }">
          ¥{{ row.totalAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag 
            :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="开嘱时间" width="150" align="center"></el-table-column>
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="editOrder(row)">修改</el-button>
          <el-button size="small" type="success" @click="executeOrder(row)" v-if="row.status === 'pending'">执行</el-button>
          <el-button size="small" type="warning" @click="stopOrder(row)" v-if="row.status === 'executing'">停止</el-button>
          <el-button size="small" type="danger" @click="deleteOrder(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div>

    <!-- 新增/编辑医嘱弹窗 -->
    <el-dialog 
      :title="dialogTitle" 
      v-model="dialogVisible" 
      width="60%"
      :before-close="handleClose">
      <el-form :model="orderForm" :rules="rules" ref="orderFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="患者姓名" prop="patientName">
              <el-input v-model="orderForm.patientName" placeholder="请输入患者姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="患者ID" prop="patientId">
              <el-input v-model="orderForm.patientId" placeholder="请输入患者ID"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="诊疗项目" prop="treatmentName">
              <el-select v-model="orderForm.treatmentName" placeholder="请选择诊疗项目" filterable>
                <el-option label="血常规" value="血常规"></el-option>
                <el-option label="心电图" value="心电图"></el-option>
                <el-option label="胸部CT" value="胸部CT"></el-option>
                <el-option label="腹部B超" value="腹部B超"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编码" prop="treatmentCode">
              <el-input v-model="orderForm.treatmentCode" placeholder="请输入项目编码"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="数量" prop="quantity">
              <el-input-number v-model="orderForm.quantity" :min="1" placeholder="数量"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单位" prop="unit">
              <el-select v-model="orderForm.unit" placeholder="请选择单位">
                <el-option label="次" value="次"></el-option>
                <el-option label="项" value="项"></el-option>
                <el-option label="例" value="例"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单价" prop="price">
              <el-input-number v-model="orderForm.price" :precision="2" :min="0" placeholder="单价"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="执行科室" prop="department">
              <el-select v-model="orderForm.department" placeholder="请选择执行科室">
                <el-option label="检验科" value="检验科"></el-option>
                <el-option label="放射科" value="放射科"></el-option>
                <el-option label="超声科" value="超声科"></el-option>
                <el-option label="心电图室" value="心电图室"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="orderForm.status" placeholder="请选择状态">
                <el-option label="待执行" value="pending"></el-option>
                <el-option label="执行中" value="executing"></el-option>
                <el-option label="已完成" value="completed"></el-option>
                <el-option label="已停止" value="stopped"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input v-model="orderForm.remark" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveOrder">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PatientTreatmentOrder',
  data() {
    return {
      searchForm: {
        patientName: '',
        treatmentName: '',
        status: ''
      },
      tableData: [
        {
          id: '1',
          patientName: '张三',
          patientId: 'P001',
          treatmentName: '血常规',
          treatmentCode: 'LAB001',
          quantity: 1,
          unit: '次',
          price: '25.00',
          totalAmount: '25.00',
          department: '检验科',
          status: 'executing',
          createTime: '2024-01-15 09:30:00',
          remark: '空腹抽血'
        },
        {
          id: '2',
          patientName: '李四',
          patientId: 'P002',
          treatmentName: '心电图',
          treatmentCode: 'ECG001',
          quantity: 1,
          unit: '次',
          price: '30.00',
          totalAmount: '30.00',
          department: '心电图室',
          status: 'pending',
          createTime: '2024-01-15 10:15:00',
          remark: '常规心电图检查'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 2,
      loading: false,
      multipleSelection: [],
      
      // 弹窗相关
      dialogVisible: false,
      dialogTitle: '新增医嘱',
      orderForm: {
        patientName: '',
        patientId: '',
        treatmentName: '',
        treatmentCode: '',
        quantity: 1,
        unit: '',
        price: 0,
        department: '',
        status: 'pending',
        remark: ''
      },
      rules: {
        patientName: [{ required: true, message: '请输入患者姓名', trigger: 'blur' }],
        patientId: [{ required: true, message: '请输入患者ID', trigger: 'blur' }],
        treatmentName: [{ required: true, message: '请选择诊疗项目', trigger: 'change' }],
        treatmentCode: [{ required: true, message: '请输入项目编码', trigger: 'blur' }],
        quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        price: [{ required: true, message: '请输入单价', trigger: 'blur' }],
        department: [{ required: true, message: '请选择执行科室', trigger: 'change' }]
      }
    }
  },
  methods: {
    searchOrders() {
      this.loading = true;
      setTimeout(() => {
        console.log('搜索条件:', this.searchForm);
        this.loading = false;
        this.$message.success('查询完成');
      }, 1000);
    },
    
    resetSearch() {
      this.searchForm = {
        patientName: '',
        treatmentName: '',
        status: ''
      };
      this.searchOrders();
    },
    
    addOrder() {
      this.dialogTitle = '新增医嘱';
      this.orderForm = {
        patientName: '',
        patientId: '',
        treatmentName: '',
        treatmentCode: '',
        quantity: 1,
        unit: '',
        price: 0,
        department: '',
        status: 'pending',
        remark: ''
      };
      this.dialogVisible = true;
    },
    
    editOrder(row) {
      this.dialogTitle = '编辑医嘱';
      this.orderForm = { ...row };
      this.dialogVisible = true;
    },
    
    saveOrder() {
      this.$refs.orderFormRef.validate((valid) => {
        if (valid) {
          // 计算总金额
          this.orderForm.totalAmount = (this.orderForm.quantity * this.orderForm.price).toFixed(2);
          console.log('保存医嘱:', this.orderForm);
          this.dialogVisible = false;
          this.$message.success('保存成功');
        }
      });
    },
    
    executeOrder(row) {
      this.$confirm('确认执行该医嘱吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.status = 'executing';
        this.$message.success('医嘱已开始执行');
      });
    },
    
    stopOrder(row) {
      this.$confirm('确认停止该医嘱吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.status = 'stopped';
        this.$message.success('医嘱已停止');
      });
    },
    
    deleteOrder(row) {
      this.$confirm('确认删除该医嘱吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.tableData.indexOf(row);
        this.tableData.splice(index, 1);
        this.$message.success('删除成功');
      });
    },
    
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },
    
    batchExecute() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要执行的医嘱');
        return;
      }
      this.$confirm('确认批量执行选中的医嘱吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.multipleSelection.forEach(item => {
          if (item.status === 'pending') {
            item.status = 'executing';
          }
        });
        this.$message.success('批量执行成功');
      });
    },
    
    batchStop() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要停止的医嘱');
        return;
      }
      this.$confirm('确认批量停止选中的医嘱吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.multipleSelection.forEach(item => {
          if (item.status === 'executing') {
            item.status = 'stopped';
          }
        });
        this.$message.success('批量停止成功');
      });
    },
    
    deleteSelected() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要删除的医嘱');
        return;
      }
      this.$confirm('确认删除选中的医嘱吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.multipleSelection.forEach(item => {
          const index = this.tableData.indexOf(item);
          this.tableData.splice(index, 1);
        });
        this.$message.success('删除成功');
      });
    },
    
    getStatusType(status) {
      const statusMap = {
        'pending': 'info',
        'executing': 'success',
        'completed': 'primary',
        'stopped': 'danger'
      };
      return statusMap[status] || 'info';
    },
    
    getStatusText(status) {
      const statusMap = {
        'pending': '待执行',
        'executing': '执行中',
        'completed': '已完成',
        'stopped': '已停止'
      };
      return statusMap[status] || '未知';
    },
    
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => {});
    },
    
    handleSizeChange(val) {
      this.pageSize = val;
      this.searchOrders();
    },
    
    handleCurrentChange(val) {
      this.currentPage = val;
      this.searchOrders();
    }
  },
  
  mounted() {
    this.searchOrders();
  }
}
</script>

<style scoped>
.patient-treatment-order {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  font-size: 24px;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
}

.action-buttons {
  margin-bottom: 20px;
}

.action-buttons .el-button {
  margin-right: 10px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>
