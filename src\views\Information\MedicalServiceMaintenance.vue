<template>
  <div>
    <!-- 头部搜索栏 -->
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item label="医疗服务名称">
        <el-input v-model="searchForm.name" placeholder="医疗服务名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">搜索</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div style="margin-bottom: 10px;">
      <el-button type="primary" @click="addService">+ 增加服务</el-button>
      <el-button type="danger" @click="deleteSelected">删除选中</el-button>
    </div>

    <!-- 表格 -->
    <el-table 
      ref="multipleTable"
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="id" label="序号" width="50"></el-table-column>
      <el-table-column prop="category" label="项目类别" width="80"></el-table-column>
      <el-table-column prop="code" label="项目编码" width="150"></el-table-column>
      <el-table-column prop="name" label="项目名称"></el-table-column>
      <el-table-column prop="description" label="项目说明"></el-table-column>
      <el-table-column prop="exceptionContent" label="除外内容"></el-table-column>
      <el-table-column prop="unit" label="计价单位" width="80"></el-table-column>
      <el-table-column prop="price" label="价格" width="100"></el-table-column>
      <el-table-column prop="remark" label="备注"></el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">修改</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>

    <!-- 修改医疗服务弹窗 -->
    <el-dialog title="修改医疗服务" v-model="editDialogVisible" width="50%">
      <el-form :model="editForm" label-width="120px">
        <el-form-item label="项目类别">
          <el-input v-model="editForm.category"></el-input>
        </el-form-item>
        <el-form-item label="项目编码">
          <el-input v-model="editForm.code"></el-input>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model="editForm.name"></el-input>
        </el-form-item>
        <el-form-item label="项目说明">
          <el-input v-model="editForm.description" type="textarea" :rows="2"></el-input>
        </el-form-item>
        <el-form-item label="除外内容">
          <el-input v-model="editForm.exceptionContent" type="textarea" :rows="2"></el-input>
        </el-form-item>
        <el-form-item label="计价单位">
          <el-input v-model="editForm.unit"></el-input>
        </el-form-item>
        <el-form-item label="价格">
          <el-input-number v-model="editForm.price" :min="0" :precision="2"></el-input-number>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="editForm.remark" type="textarea" :rows="2"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitEdit">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 增加医疗服务弹窗 -->
    <el-dialog title="增加医疗服务" v-model="addDialogVisible" width="50%">
      <el-form :model="addForm" label-width="120px">
        <el-form-item label="项目类别">
          <el-input v-model="addForm.category"></el-input>
        </el-form-item>
        <el-form-item label="项目编码">
          <el-input v-model="addForm.code"></el-input>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model="addForm.name"></el-input>
        </el-form-item>
        <el-form-item label="项目说明">
          <el-input v-model="addForm.description" type="textarea" :rows="2"></el-input>
        </el-form-item>
        <el-form-item label="除外内容">
          <el-input v-model="addForm.exceptionContent" type="textarea" :rows="2"></el-input>
        </el-form-item>
        <el-form-item label="计价单位">
          <el-input v-model="addForm.unit"></el-input>
        </el-form-item>
        <el-form-item label="价格">
          <el-input-number v-model="addForm.price" :min="0" :precision="2"></el-input-number>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="addForm.remark" type="textarea" :rows="2"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitAdd">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        name: ''
      },
      tableData: [
        {
          id: '1',
          category: 'C',
          code: '110200001-1',
          name: '门诊诊查费(医师)',
          description: '指医护人员提供(技术劳务)的诊疗服务',
          exceptionContent: '',
          unit: '次',
          price: 3,
          remark: ''
        },
        {
          id: '2',
          category: 'A',
          code: '110200002-1',
          name: '急诊诊查费',
          description: '急诊科医师诊查费用',
          exceptionContent: '不含抢救费',
          unit: '次',
          price: 5,
          remark: '急诊科专用'
        },
        {
          id: '3',
          category: 'B',
          code: '110200003-1',
          name: '专家诊查费',
          description: '副主任医师及以上诊查费',
          exceptionContent: '',
          unit: '次',
          price: 15,
          remark: '专家门诊'
        },
        {
          id: '4',
          category: 'D',
          code: '110200004-1',
          name: '护理费',
          description: '住院期间护理服务费用',
          exceptionContent: '不含特殊护理',
          unit: '天',
          price: 12,
          remark: '一般护理'
        },
        {
          id: '5',
          category: 'E',
          code: '110200005-1',
          name: '床位费',
          description: '住院床位使用费',
          exceptionContent: '不含特殊病房',
          unit: '天',
          price: 28,
          remark: '普通病房'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 5,
      multipleSelection: [],
      
      // 修改弹窗相关数据
      editDialogVisible: false,
      editForm: {
        id: '',
        category: '',
        code: '',
        name: '',
        description: '',
        exceptionContent: '',
        unit: '',
        price: 0,
        remark: ''
      },
      
      // 增加弹窗相关数据
      addDialogVisible: false,
      addForm: {
        id: '',
        category: '',
        code: '',
        name: '',
        description: '',
        exceptionContent: '',
        unit: '',
        price: 0,
        remark: ''
      }
    }
  },
  methods: {
    onSubmit() {
      console.log('搜索:', this.searchForm.name);
    },
    addService() {
      this.addDialogVisible = true;
      // 初始化新增表单数据
      this.addForm = {
        id: (this.tableData.length + 1).toString(),
        category: '',
        code: '',
        name: '',
        description: '',
        exceptionContent: '',
        unit: '',
        price: 0,
        remark: ''
      };
    },
    deleteSelected() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一项');
        return;
      }

      this.$confirm('确定要删除选中的项目吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const selectedIds = this.multipleSelection.map(row => row.id);
        this.tableData = this.tableData.filter(item => !selectedIds.includes(item.id));
        this.total = this.tableData.length;
        this.$refs.multipleTable.clearSelection();
        this.$message.success('删除成功');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },
    handleEdit(index, row) {
      this.editDialogVisible = true;
      this.editForm = { ...row };
    },
    submitEdit() {
      // 更新表格数据
      const index = this.tableData.findIndex(item => item.id === this.editForm.id);
      if (index !== -1) {
        this.tableData.splice(index, 1, this.editForm);
      }
      this.editDialogVisible = false;
      this.$message.success('修改成功');
    },
    submitAdd() {
      // 将新项目添加到表格数据
      this.tableData.push({ ...this.addForm });
      this.total = this.tableData.length;
      this.addDialogVisible = false;
      this.$message.success('新增成功');
    },
    handleDelete(index, row) {
      this.$confirm('确定要删除该项目吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableData.splice(index, 1);
        this.total = this.tableData.length;
        this.$message.success('删除成功');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    }
  }
}
</script>

<style>
.el-table .cell {
  text-align: center;
}

/* 调整表单布局 */
.el-form-item {
  margin-bottom: 15px;
}

.el-input, .el-textarea {
  width: 100%;
}
</style>