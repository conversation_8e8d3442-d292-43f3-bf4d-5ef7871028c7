<template>
  <div class="login-page">
    <div class="auth-container">
      <h2>用户登录</h2>
      <form @submit.prevent="handleLogin">
        <div class="form-group">
          <label>用户名</label>
          <input 
            type="text" 
            v-model="formData.username" 
            required
            placeholder="请输入用户名"
          >
        </div>
        <div class="form-group">
          <label>密码</label>
          <input 
            type="password" 
            v-model="formData.password" 
            required
            placeholder="请输入密码"
          >
        </div>
        <button type="submit" class="submit-btn">登录</button>
        <p class="toggle-text">
          没有账号？<router-link to="/register">立即注册</router-link>
        </p>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const formData = ref({
  username: '',
  password: ''
})

const handleLogin = () => {
  console.log('登录信息:', formData.value)
  // 实际项目调用API
  alert(`欢迎回来, ${formData.value.username}!`)
  router.push('/admissionRegistration') // 登录成功后跳转到入院登记页面
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-image: url('@/assets/logbg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.login-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 0;
}

/* 加宽登录表单容器 */
.auth-container {
  width: 500px; /* 增加宽度 */
  max-width: 90%; /* 在小屏幕上保持响应式 */
  padding: 40px; /* 增加内边距 */
  background: rgba(255, 255, 255, 0.92); /* 提高背景不透明度 */
  border-radius: 12px; /* 增加圆角半径 */
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3); /* 增强阴影效果 */
  position: relative;
  z-index: 1;
  backdrop-filter: blur(8px); /* 增强模糊效果 */
}

h2 {
  text-align: center;
  margin-bottom: 30px; /* 增加底部间距 */
  color: #333;
  font-size: 28px; /* 增大标题字号 */
}

.form-group {
  margin-bottom: 25px; /* 增加间距 */
}

.form-group label {
  display: block;
  margin-bottom: 10px; /* 增加标签间距 */
  font-weight: 500;
  color: #555;
  font-size: 16px; /* 增大标签字号 */
}

.form-group input {
  width: 100%;
  padding: 14px; /* 增加输入框内边距 */
  border: 1px solid #dcdfe6;
  border-radius: 6px; /* 增加圆角 */
  font-size: 16px; /* 增大输入文字字号 */
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.form-group input:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.25); /* 增强焦点效果 */
}

.form-group input::placeholder {
  color: #a0a4ac;
  font-size: 15px; /* 增大占位符字号 */
}

.submit-btn {
  width: 100%;
  padding: 15px; /* 增加按钮内边距 */
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%); /* 渐变按钮 */
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 18px; /* 增大按钮文字字号 */
  font-weight: 600; /* 增加字体粗细 */
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 15px; /* 增加顶部间距 */
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3); /* 添加按钮阴影 */
}

.submit-btn:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #5a9cff 100%);
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(64, 158, 255, 0.4);
}

.submit-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.toggle-text {
  text-align: center;
  margin-top: 25px; /* 增加顶部间距 */
  color: #606266;
  font-size: 16px; 
}

.toggle-text a {
  color: #409eff;
  text-decoration: none;
  font-weight: 600; 
  transition: all 0.3s ease;
  position: relative;
}

.toggle-text a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: #66b1ff;
  transition: width 0.3s ease;
}

.toggle-text a:hover {
  color: #66b1ff;
}

.toggle-text a:hover::after {
  width: 100%;
}

@media (max-width: 600px) {
  .auth-container {
    width: 90%;
    padding: 30px;
  }
  
  h2 {
    font-size: 24px;
  }
  
  .form-group input {
    padding: 12px;
    font-size: 15px;
  }
  
  .submit-btn {
    padding: 14px;
    font-size: 16px;
  }
}
</style>