<template>
  <div class="first-level-hospital-ratio">
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>一级医院 费用阶梯（元）</span>
            <el-button type="primary" plain size="small" @click="addReimbursement">新增</el-button>
          </div>
          <el-table :data="reimbursementList" style="width: 100%">
            <el-table-column prop="threshold" label="起付线" width="120"></el-table-column>
            <el-table-column prop="level" label="等级线" width="120"></el-table-column>
            <el-table-column label="人员类别" width="120">
              <template #default="{ row }">
                <el-tag :type="row.personType === '退休人员' ? 'success' : ''">{{ row.personType }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="ratio" label="报销比例" width="120">
              <template #default="{ row }">
                <el-tag type="info">{{ row.ratio }}%</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="120">
              <template #default="{ row }">
                <el-tag v-if="row.status === '启用'" type="success">启用</el-tag>
                <el-tag v-else type="danger">停用</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="{ row }">
                <el-button type="text" @click="editReimbursement(row)">编辑</el-button>
                <el-button type="text" @click="deleteReimbursement(row)" style="color: #F56C6C;">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>详细信息</span>
            <el-alert
              title="请点击左侧菜单列表选择"
              type="info"
              :closable="false"
              show-icon>
            </el-alert>
          </div>
          <el-form :model="form" label-width="80px" ref="reimbursementForm">
            <el-form-item label="起付线" prop="threshold">
              <el-input v-model.number="form.threshold" placeholder="请输入起付线"></el-input>
            </el-form-item>
            <el-form-item label="等级线" prop="level">
              <el-input v-model.number="form.level" placeholder="请输入等级线"></el-input>
            </el-form-item>
            <el-form-item label="人员类别" prop="personType">
              <el-radio-group v-model="form.personType">
                <el-radio label="在职人员"></el-radio>
                <el-radio label="退休人员"></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="报销比例" prop="ratio">
              <el-input-number v-model="form.ratio" :min="0" :max="100" :precision="0"></el-input-number>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="启用"></el-radio>
                <el-radio label="停用"></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveReimbursement">保存</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  data() {
    return {
      reimbursementList: [
        { id: 1, threshold: 1300, level: 30000, personType: '退休人员', ratio: 94, status: '启用' },
        { id: 2, threshold: 30000, level: 40000, personType: '退休人员', ratio: 97, status: '启用' },
        { id: 3, threshold: 40000, level: 999999, personType: '退休人员', ratio: 97, status: '启用' },
        { id: 4, threshold: 1800, level: 30000, personType: '在职人员', ratio: 90, status: '启用' },
        { id: 5, threshold: 30000, level: 40000, personType: '在职人员', ratio: 95, status: '启用' },
        { id: 6, threshold: 40000, level: 999999, personType: '在职人员', ratio: 95, status: '启用' }
      ],
      form: {
        id: null,
        threshold: null,
        level: null,
        personType: '在职人员',
        ratio: null,
        status: '启用'
      }
    };
  },
  methods: {
    addReimbursement() {
      this.form = {
        id: null,
        threshold: null,
        level: null,
        personType: '在职人员',
        ratio: null,
        status: '启用'
      };
    },
    editReimbursement(row) {
      this.form = { ...row };
    },
    deleteReimbursement(row) {
      this.$confirm('确定要删除这条报销比例吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.reimbursementList = this.reimbursementList.filter(item => item.id !== row.id);
        this.$message.success('删除成功');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    saveReimbursement() {
      if (this.form.id) {
        const index = this.reimbursementList.findIndex(item => item.id === this.form.id);
        if (index !== -1) {
          this.reimbursementList.splice(index, 1, { ...this.form });
        }
      } else {
        const newId = Math.max(0, ...this.reimbursementList.map(item => item.id)) + 1;
        this.reimbursementList.push({
          id: newId,
          ...this.form
        });
      }
      this.$message.success('保存成功');
      this.resetForm();
    },
    resetForm() {
      this.form = {
        id: null,
        threshold: null,
        level: null,
        personType: '在职人员',
        ratio: null,
        status: '启用'
      };
    }
  }
};
</script>

<style scoped>
.first-level-hospital-ratio {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>