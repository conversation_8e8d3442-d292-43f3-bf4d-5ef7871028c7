<template>
  <div class="register-page">
    <div class="auth-container">
      <h2>用户注册</h2>
      <form @submit.prevent="handleRegister">
        <div class="form-group">
          <label>用户名</label>
          <input 
            type="text" 
            v-model="formData.username" 
            required
            placeholder="请输入用户名"
          >
        </div>
        <div class="form-group">
          <label>密码</label>
          <input 
            type="password" 
            v-model="formData.password" 
            required
            placeholder="请输入密码"
          >
        </div>
        <div class="form-group">
          <label>确认密码</label>
          <input 
            type="password" 
            v-model="formData.confirmPassword" 
            required
            placeholder="请再次输入密码"
          >
        </div>
        <button type="submit" class="submit-btn">注册</button>
        <p class="toggle-text">
          已有账号？<router-link to="/login">立即登录</router-link>
        </p>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const formData = ref({
  username: '',
  password: '',
  confirmPassword: ''
})

const handleRegister = () => {
  if (formData.value.password !== formData.value.confirmPassword) {
    alert('两次输入的密码不一致')
    return
  }
  console.log('注册信息:', formData.value)
  // 实际项目调用API
  alert('注册成功!')
  router.push('/login') // 注册成功后跳转到登录页
}
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-image: url('@/assets/logbg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.register-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 0;
}

/* 整体缩小尺寸 */
.auth-container {
  width: 520px; /* 缩小宽度 */
  max-width: 85%; 
  padding: 40px; /* 缩小内边距 */
  background: rgba(255, 255, 255, 0.95);
  border-radius: 14px; /* 缩小圆角 */
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3); /* 缩小阴影 */
  position: relative;
  z-index: 1;
  backdrop-filter: blur(8px); /* 减小模糊 */
}

h2 {
  text-align: center;
  margin-bottom: 32px; /* 缩小间距 */
  color: #333;
  font-size: 28px; /* 缩小字体 */
  font-weight: 700; 
}

.form-group {
  margin-bottom: 24px; /* 缩小间距 */
}

.form-group label {
  display: block;
  margin-bottom: 10px; /* 缩小间距 */
  font-weight: 600; 
  color: #444;
  font-size: 16px; /* 缩小字体 */
}

.form-group input {
  width: 100%;
  padding: 14px 18px; /* 缩小内边距 */
  border: 1px solid #dcdfe6;
  border-radius: 6px; /* 缩小圆角 */
  font-size: 16px; /* 缩小字体 */
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
}

.form-group input:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.3); /* 缩小效果 */
}

.form-group input::placeholder {
  color: #a0a4ac;
  font-size: 14px; /* 缩小字体 */
}

.submit-btn {
  width: 100%;
  padding: 16px; /* 缩小内边距 */
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%); 
  color: white;
  border: none;
  border-radius: 6px; /* 缩小圆角 */
  font-size: 18px; /* 缩小字体 */
  font-weight: 700; 
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 16px; /* 缩小间距 */
  box-shadow: 0 5px 12px rgba(64, 158, 255, 0.4); /* 缩小阴影 */
  letter-spacing: 0.8px; /* 缩小字母间距 */
}

.submit-btn:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #5a9cff 100%);
  transform: translateY(-3px); /* 缩小效果 */
  box-shadow: 0 8px 16px rgba(64, 158, 255, 0.5); /* 缩小阴影 */
}

.submit-btn:active {
  transform: translateY(1px); /* 缩小效果 */
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3); /* 缩小阴影 */
}

.toggle-text {
  text-align: center;
  margin-top: 24px; /* 缩小间距 */
  color: #606266;
  font-size: 16px; /* 缩小字体 */
  font-weight: 500;
}

.toggle-text a {
  color: #409eff;
  text-decoration: none;
  font-weight: 700; 
  transition: all 0.3s ease;
  position: relative;
  padding: 0 4px; /* 缩小内边距 */
}

.toggle-text a::after {
  content: '';
  position: absolute;
  bottom: -2px; /* 缩小位置 */
  left: 0;
  width: 0;
  height: 2px; /* 缩小高度 */
  background: #66b1ff;
  transition: width 0.4s ease;
  border-radius: 1px; /* 缩小圆角 */
}

.toggle-text a:hover {
  color: #66b1ff;
}

.toggle-text a:hover::after {
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .auth-container {
    width: 90%;
    padding: 30px; /* 缩小内边距 */
  }
  
  h2 {
    font-size: 24px; /* 缩小字体 */
    margin-bottom: 24px; /* 缩小间距 */
  }
  
  .form-group input {
    padding: 12px 15px; /* 缩小内边距 */
    font-size: 15px; /* 缩小字体 */
  }
  
  .submit-btn {
    padding: 14px; /* 缩小内边距 */
    font-size: 16px; /* 缩小字体 */
  }
  
  .toggle-text {
    font-size: 15px; /* 缩小字体 */
  }
}

@media (max-width: 480px) {
  .auth-container {
    padding: 25px; /* 缩小内边距 */
  }
  
  h2 {
    font-size: 22px; /* 缩小字体 */
  }
  
  .form-group input {
    padding: 10px 14px; /* 缩小内边距 */
  }
}
</style>