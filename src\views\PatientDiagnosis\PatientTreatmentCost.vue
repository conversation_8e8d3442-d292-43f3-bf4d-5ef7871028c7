<template>
  <div class="patient-treatment-cost">
    <div class="page-header">
      <h2>患者诊疗费用信息维护</h2>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="患者姓名">
          <el-input v-model="searchForm.patientName" placeholder="请输入患者姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="诊疗项目">
          <el-input v-model="searchForm.treatmentName" placeholder="请输入诊疗项目" clearable></el-input>
        </el-form-item>
        <el-form-item label="费用状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="未结算" value="unpaid"></el-option>
            <el-option label="已结算" value="paid"></el-option>
            <el-option label="已退费" value="refunded"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchCosts">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="addCost">新增费用</el-button>
      <el-button type="success" @click="batchSettle">批量结算</el-button>
      <el-button type="warning" @click="batchRefund">批量退费</el-button>
      <el-button type="danger" @click="deleteSelected">删除选中</el-button>
    </div>

    <!-- 数据表格 -->
    <el-table 
      :data="tableData" 
      border 
      style="width: 100%"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      show-summary
      :summary-method="getSummaries">
      <el-table-column type="selection" width="55" align="center"></el-table-column>
      <el-table-column prop="id" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="patientName" label="患者姓名" width="100"></el-table-column>
      <el-table-column prop="patientId" label="患者ID" width="120"></el-table-column>
      <el-table-column prop="treatmentName" label="诊疗项目" min-width="150"></el-table-column>
      <el-table-column prop="treatmentCode" label="项目编码" width="120"></el-table-column>
      <el-table-column prop="category" label="项目类别" width="100" align="center"></el-table-column>
      <el-table-column prop="quantity" label="数量" width="80" align="center"></el-table-column>
      <el-table-column prop="unit" label="单位" width="80" align="center"></el-table-column>
      <el-table-column prop="unitPrice" label="单价" width="100" align="center">
        <template #default="{ row }">
          ¥{{ row.unitPrice }}
        </template>
      </el-table-column>
      <el-table-column prop="totalAmount" label="总金额" width="100" align="center">
        <template #default="{ row }">
          ¥{{ row.totalAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="insuranceRatio" label="报销比例" width="100" align="center">
        <template #default="{ row }">
          {{ row.insuranceRatio }}%
        </template>
      </el-table-column>
      <el-table-column prop="insuranceAmount" label="报销金额" width="100" align="center">
        <template #default="{ row }">
          ¥{{ row.insuranceAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="selfPayAmount" label="自付金额" width="100" align="center">
        <template #default="{ row }">
          ¥{{ row.selfPayAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag 
            :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="费用时间" width="150" align="center"></el-table-column>
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="editCost(row)">修改</el-button>
          <el-button size="small" type="success" @click="settleCost(row)" v-if="row.status === 'unpaid'">结算</el-button>
          <el-button size="small" type="warning" @click="refundCost(row)" v-if="row.status === 'paid'">退费</el-button>
          <el-button size="small" type="danger" @click="deleteCost(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div>

    <!-- 新增/编辑费用弹窗 -->
    <el-dialog 
      :title="dialogTitle" 
      v-model="dialogVisible" 
      width="60%"
      :before-close="handleClose">
      <el-form :model="costForm" :rules="rules" ref="costFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="患者姓名" prop="patientName">
              <el-input v-model="costForm.patientName" placeholder="请输入患者姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="患者ID" prop="patientId">
              <el-input v-model="costForm.patientId" placeholder="请输入患者ID"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="诊疗项目" prop="treatmentName">
              <el-select v-model="costForm.treatmentName" placeholder="请选择诊疗项目" filterable>
                <el-option label="血常规" value="血常规"></el-option>
                <el-option label="心电图" value="心电图"></el-option>
                <el-option label="胸部CT" value="胸部CT"></el-option>
                <el-option label="腹部B超" value="腹部B超"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编码" prop="treatmentCode">
              <el-input v-model="costForm.treatmentCode" placeholder="请输入项目编码"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目类别" prop="category">
              <el-select v-model="costForm.category" placeholder="请选择项目类别">
                <el-option label="检验" value="检验"></el-option>
                <el-option label="检查" value="检查"></el-option>
                <el-option label="治疗" value="治疗"></el-option>
                <el-option label="手术" value="手术"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="执行科室" prop="department">
              <el-select v-model="costForm.department" placeholder="请选择执行科室">
                <el-option label="检验科" value="检验科"></el-option>
                <el-option label="放射科" value="放射科"></el-option>
                <el-option label="超声科" value="超声科"></el-option>
                <el-option label="心电图室" value="心电图室"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="数量" prop="quantity">
              <el-input-number v-model="costForm.quantity" :min="1" @change="calculateAmount"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单位" prop="unit">
              <el-select v-model="costForm.unit" placeholder="请选择单位">
                <el-option label="次" value="次"></el-option>
                <el-option label="项" value="项"></el-option>
                <el-option label="例" value="例"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单价" prop="unitPrice">
              <el-input-number v-model="costForm.unitPrice" :precision="2" :min="0" @change="calculateAmount"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="总金额">
              <el-input v-model="costForm.totalAmount" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报销比例" prop="insuranceRatio">
              <el-input-number v-model="costForm.insuranceRatio" :min="0" :max="100" @change="calculateAmount"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态" prop="status">
              <el-select v-model="costForm.status" placeholder="请选择状态">
                <el-option label="未结算" value="unpaid"></el-option>
                <el-option label="已结算" value="paid"></el-option>
                <el-option label="已退费" value="refunded"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报销金额">
              <el-input v-model="costForm.insuranceAmount" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="自付金额">
              <el-input v-model="costForm.selfPayAmount" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input v-model="costForm.remark" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCost">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PatientTreatmentCost',
  data() {
    return {
      searchForm: {
        patientName: '',
        treatmentName: '',
        status: ''
      },
      tableData: [
        {
          id: '1',
          patientName: '张三',
          patientId: 'P001',
          treatmentName: '血常规',
          treatmentCode: 'LAB001',
          category: '检验',
          quantity: 1,
          unit: '次',
          unitPrice: '25.00',
          totalAmount: '25.00',
          insuranceRatio: 90,
          insuranceAmount: '22.50',
          selfPayAmount: '2.50',
          department: '检验科',
          status: 'paid',
          createTime: '2024-01-15 09:30:00',
          remark: '空腹抽血'
        },
        {
          id: '2',
          patientName: '李四',
          patientId: 'P002',
          treatmentName: '心电图',
          treatmentCode: 'ECG001',
          category: '检查',
          quantity: 1,
          unit: '次',
          unitPrice: '30.00',
          totalAmount: '30.00',
          insuranceRatio: 85,
          insuranceAmount: '25.50',
          selfPayAmount: '4.50',
          department: '心电图室',
          status: 'unpaid',
          createTime: '2024-01-15 10:15:00',
          remark: '常规心电图检查'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 2,
      loading: false,
      multipleSelection: [],
      
      // 弹窗相关
      dialogVisible: false,
      dialogTitle: '新增费用',
      costForm: {
        patientName: '',
        patientId: '',
        treatmentName: '',
        treatmentCode: '',
        category: '',
        quantity: 1,
        unit: '',
        unitPrice: 0,
        totalAmount: '0.00',
        insuranceRatio: 0,
        insuranceAmount: '0.00',
        selfPayAmount: '0.00',
        department: '',
        status: 'unpaid',
        remark: ''
      },
      rules: {
        patientName: [{ required: true, message: '请输入患者姓名', trigger: 'blur' }],
        patientId: [{ required: true, message: '请输入患者ID', trigger: 'blur' }],
        treatmentName: [{ required: true, message: '请选择诊疗项目', trigger: 'change' }],
        treatmentCode: [{ required: true, message: '请输入项目编码', trigger: 'blur' }],
        category: [{ required: true, message: '请选择项目类别', trigger: 'change' }],
        quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur' }],
        department: [{ required: true, message: '请选择执行科室', trigger: 'change' }]
      }
    }
  },
  methods: {
    searchCosts() {
      this.loading = true;
      setTimeout(() => {
        console.log('搜索条件:', this.searchForm);
        this.loading = false;
        this.$message.success('查询完成');
      }, 1000);
    },
    
    resetSearch() {
      this.searchForm = {
        patientName: '',
        treatmentName: '',
        status: ''
      };
      this.searchCosts();
    },
    
    addCost() {
      this.dialogTitle = '新增费用';
      this.costForm = {
        patientName: '',
        patientId: '',
        treatmentName: '',
        treatmentCode: '',
        category: '',
        quantity: 1,
        unit: '',
        unitPrice: 0,
        totalAmount: '0.00',
        insuranceRatio: 0,
        insuranceAmount: '0.00',
        selfPayAmount: '0.00',
        department: '',
        status: 'unpaid',
        remark: ''
      };
      this.dialogVisible = true;
    },
    
    editCost(row) {
      this.dialogTitle = '编辑费用';
      this.costForm = { ...row };
      this.dialogVisible = true;
    },
    
    calculateAmount() {
      const total = this.costForm.quantity * this.costForm.unitPrice;
      this.costForm.totalAmount = total.toFixed(2);
      
      const insuranceAmount = total * (this.costForm.insuranceRatio / 100);
      this.costForm.insuranceAmount = insuranceAmount.toFixed(2);
      
      const selfPayAmount = total - insuranceAmount;
      this.costForm.selfPayAmount = selfPayAmount.toFixed(2);
    },
    
    saveCost() {
      this.$refs.costFormRef.validate((valid) => {
        if (valid) {
          console.log('保存费用:', this.costForm);
          this.dialogVisible = false;
          this.$message.success('保存成功');
        }
      });
    },
    
    settleCost(row) {
      this.$confirm('确认结算该费用吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.status = 'paid';
        this.$message.success('费用已结算');
      });
    },
    
    refundCost(row) {
      this.$confirm('确认退费吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.status = 'refunded';
        this.$message.success('退费成功');
      });
    },
    
    deleteCost(row) {
      this.$confirm('确认删除该费用记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.tableData.indexOf(row);
        this.tableData.splice(index, 1);
        this.$message.success('删除成功');
      });
    },
    
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },
    
    batchSettle() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要结算的费用');
        return;
      }
      this.$confirm('确认批量结算选中的费用吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.multipleSelection.forEach(item => {
          if (item.status === 'unpaid') {
            item.status = 'paid';
          }
        });
        this.$message.success('批量结算成功');
      });
    },
    
    batchRefund() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要退费的费用');
        return;
      }
      this.$confirm('确认批量退费选中的费用吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.multipleSelection.forEach(item => {
          if (item.status === 'paid') {
            item.status = 'refunded';
          }
        });
        this.$message.success('批量退费成功');
      });
    },
    
    deleteSelected() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要删除的费用');
        return;
      }
      this.$confirm('确认删除选中的费用记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.multipleSelection.forEach(item => {
          const index = this.tableData.indexOf(item);
          this.tableData.splice(index, 1);
        });
        this.$message.success('删除成功');
      });
    },
    
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        if (column.property === 'totalAmount' || 
            column.property === 'insuranceAmount' || 
            column.property === 'selfPayAmount') {
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = '¥' + values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0).toFixed(2);
          } else {
            sums[index] = '';
          }
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
    
    getStatusType(status) {
      const statusMap = {
        'unpaid': 'warning',
        'paid': 'success',
        'refunded': 'info'
      };
      return statusMap[status] || 'info';
    },
    
    getStatusText(status) {
      const statusMap = {
        'unpaid': '未结算',
        'paid': '已结算',
        'refunded': '已退费'
      };
      return statusMap[status] || '未知';
    },
    
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => {});
    },
    
    handleSizeChange(val) {
      this.pageSize = val;
      this.searchCosts();
    },
    
    handleCurrentChange(val) {
      this.currentPage = val;
      this.searchCosts();
    }
  },
  
  mounted() {
    this.searchCosts();
  }
}
</script>

<style scoped>
.patient-treatment-cost {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  font-size: 24px;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
}

.action-buttons {
  margin-bottom: 20px;
}

.action-buttons .el-button {
  margin-right: 10px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>
