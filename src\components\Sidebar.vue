<template>
  <el-aside width="200px">
    <el-menu
      router
      :default-active="$route.path"
      mode="vertical"
      :collapse="false"
      background-color="#545c64"
      text-color="#fff"
      active-text-color="#ffd04b"
    >
      <el-menu-item index="/admissionRegistration">
        <span>入院登记</span>
      </el-menu-item>

      <el-sub-menu index="0">
        <template #title>
          <span>信息查询</span>
        </template>
        <el-menu-item index="/drugQuery">药品信息查询</el-menu-item>
        <el-menu-item index="/treatmentQuery">诊疗信息查询</el-menu-item>
        <el-menu-item index="/serviceQuery">医疗服务信息查询</el-menu-item>
      </el-sub-menu>

      <el-sub-menu index="1">
        <template #title>
          <span>数据信息维护</span>
        </template>
        <el-menu-item index="/drugMaintenance">医保药品数据维护</el-menu-item>
        <el-menu-item index="/treatmentMaintenance">诊疗项目数据维护</el-menu-item>
        <el-menu-item index="/medicalServiceMaintenance">医疗服务设施数据维护</el-menu-item>
      </el-sub-menu>

      <el-sub-menu index="2">
        <template #title>
          <span>报销比例维护</span>
        </template>
        <el-menu-item index="/drugReimbursementRatio">药品报销比例</el-menu-item>
        <el-menu-item index="/thirdLevelHospitalRatio">三级医院报销比例</el-menu-item>
        <el-menu-item index="/secondLevelHospitalRatio">二级医院报销比例</el-menu-item>
        <el-menu-item index="/firstLevelHospitalRatio">一级医院报销比例</el-menu-item>
      </el-sub-menu>

      <el-sub-menu index="5">
        <template #title>
          <span>报销管理</span>
        </template>
        <el-menu-item index="/multiPersonDrugQuery">多个人员用药信息查询</el-menu-item>
        <el-menu-item index="/multiPersonDrugStats">多个人员用药信息统计</el-menu-item>
        <el-menu-item index="/multiPersonCostDetail">多个人员费用明细</el-menu-item>
        <el-menu-item index="/multiPersonMedicalOrder">多个人员医嘱</el-menu-item>
      </el-sub-menu>

      <el-sub-menu index="3">
        <template #title>
          <span>患者医嘱</span>
        </template>
        <el-menu-item index="/patientDrugOrder">药品医嘱信息维护</el-menu-item>
        <el-menu-item index="/patientTreatmentOrder">诊疗医嘱信息维护</el-menu-item>
        <el-menu-item index="/patientServiceOrder">医疗服务医嘱信息维护</el-menu-item>
      </el-sub-menu>

      <el-sub-menu index="4">
        <template #title>
          <span>患者诊断</span>
        </template>
        <el-menu-item index="/patientDrugCost">患者药品费用信息维护</el-menu-item>
        <el-menu-item index="/patientTreatmentCost">患者诊疗费用信息维护</el-menu-item>
      </el-sub-menu>

    </el-menu>
  </el-aside>
</template>

<script>
export default {
  name: 'Sidebar'
}
</script>

<style scoped>
.el-aside {
  background-color: #545c64;
}

.el-menu {
  border-right: none;
  background-color: #545c64 !important;
}

.el-menu-item {
  height: 50px;
  line-height: 50px;
  color: #fff !important;
}

.el-menu-item:hover {
  background-color: #434a50 !important;
}

.el-menu-item.is-active {
  background-color: #ffd04b !important;
  color: #000 !important;
}

.el-sub-menu .el-menu-item {
  height: 45px;
  line-height: 45px;
  padding-left: 40px !important;
  background-color: #434a50 !important;
}

.el-sub-menu .el-menu-item:hover {
  background-color: #363c41 !important;
}

.el-sub-menu__title {
  color: #fff !important;
  height: 50px;
  line-height: 50px;
  text-align: left !important;
  padding-left: 20px !important;
  justify-content: flex-start !important;
}

.el-sub-menu__title:hover {
  background-color: #434a50 !important;
}

.el-sub-menu__title span {
  margin-left: 0 !important;
}

/* 隐藏子菜单的默认箭头图标 */
.el-sub-menu__icon-arrow {
  display: none !important;
}

/* 隐藏子菜单标题中的所有图标 */
.el-sub-menu__title .el-icon {
  display: none !important;
}

/* 强制子菜单标题左对齐 */
.el-sub-menu > .el-sub-menu__title {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  padding-left: 20px !important;
}

.el-sub-menu > .el-sub-menu__title span {
  text-align: left !important;
  margin-left: 0 !important;
  padding-left: 0 !important;
}
</style>